import {RoleStatus} from 'Pages/RolesResponsibilitiesPage/roles.util';
import {UserStatus} from 'Pages/UserManagementPage/userManagement.utils';
import {buildUserViewFilterDto, IRole, IUserFilterModel, UserViewType} from 'redux/app/types';
import {buildRoleFilterDto, IRoleFilterModel} from 'redux/app/types/rolesDto.types';
import {apiSlice, BaseQueryParams} from '../apiSlice';
import {User} from './user.model';

/**
 * Interface for login form credentials.
 */
export interface ILoginForm {
  username: string;
  password: string;
}

/**
 * Interface for token retrieval.
 */
export interface IToken {
  code: string;
}

/**
 * Interface for forgot password request.
 */
export interface IForgotPassword {
  email: string;
}

/**
 * Interface for reset password request.
 */
export interface IResetPassword {
  newPassword: string;
  token: string;
}

export interface IRoleView {
  roleName: string;
  roleId: string;
  userCount: number;
  status: number;
  tenantId: string;
  createdOn: string;
  modifiedOn: string;
  deleted: boolean;
}

/**
 * Interface for activation password request.
 */
export type IActivationPassword = IResetPassword;

/**
 * RTK Query API slice for authentication and user management endpoints.
 * Injects endpoints for login, logout, token, password management, and tenant user queries.
 */

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    /**
     * Login mutation - authenticates user with credentials.
     */
    login: builder.mutation({
      query: (credentials: ILoginForm) => ({
        url: '/auth/login',
        method: 'POST',
        body: {...credentials},
      }),
    }),
    /**
     * Token mutation - retrieves a token using a code.
     */
    getToken: builder.mutation({
      query: (credentials: IToken) => ({
        url: '/auth/token',
        method: 'POST',
        body: {...credentials},
      }),
    }),
    /**
     * Forgot password mutation - sends a password reset email.
     */
    forgotPassword: builder.mutation({
      query: (credentials: IForgotPassword) => ({
        url: '/auth/forget-password',
        method: 'POST',
        body: {...credentials},
      }),
    }),
    /**
     * Reset password mutation - resets password using a token.
     */
    resetPassword: builder.mutation({
      query: (credentials: BaseQueryParams<IResetPassword>) => ({
        url: '/auth/forget-password/verify',
        method: 'POST',
        body: {newPassword: credentials.newPassword},
        headers: {
          Authorization: `Bearer ${credentials.token}`,
        },
        noBaseErrorHandling: credentials.noCommonErrorHandler,
      }),
    }),
    /**
     * Logout mutation - logs out the user and invalidates the refresh token.
     */
    logout: builder.mutation({
      query: (refreshToken: string | null) => ({
        url: '/auth/logout',
        method: 'POST',
        body: {refreshToken},
      }),
    }),
    /**
     * Query to get the current authenticated user.
     */
    getUser: builder.query<User, void>({
      query: () => ({url: '/auth/me'}),
    }),
    getRolesByGroup: builder.query<IRoleView[], IRoleFilterModel>({
      query: filter => {
        return {
          url: `/roles/role-view`,
          method: 'GET',
          params: {
            filter: JSON.stringify(buildRoleFilterDto(filter)),
          },
        };
      },
    }),
    getCountOfRolesByGroup: builder.query<{count: number}, IRoleFilterModel>({
      query: filter => {
        const refinedFilter = buildRoleFilterDto(filter).where;
        return {
          url: `/roles/role-view/count`,
          method: 'GET',
          params: {
            where: refinedFilter ? JSON.stringify(refinedFilter) : undefined,
          },
        };
      },
    }),
    /**
     * Query to get tenant users with optional offset/filter.
     * This endpoint is used to retrieve a list of users belonging to a specific tenant.
     */
    getUserLists: builder.query<UserViewType[], IUserFilterModel>({
      query: params => ({
        url: `/users/user-list`,
        method: 'GET',
        params: {
          filter: JSON.stringify(buildUserViewFilterDto(params)),
        },
      }),
    }),
    /**
     * Query to get the count of tenant users.
     * This endpoint is used to retrieve the total number of users in a tenant.
     */
    getUsersCount: builder.query<{count: number}, IUserFilterModel>({
      query: params => ({
        url: '/users/count',
        method: 'GET',
        params: {
          where: JSON.stringify(buildUserViewFilterDto(params).where),
        },
      }),
    }),

    setActivationPassword: builder.mutation<unknown, BaseQueryParams<IActivationPassword>>({
      query: credentials => ({
        url: '/auth/set-password/verify',
        method: 'POST',
        body: {newPassword: credentials.newPassword},
        headers: {
          Authorization: `Bearer ${credentials.token}`,
        },
        noBaseErrorHandling: credentials.noCommonErrorHandler,
      }),
    }),
    /**
     * Update user status by user id.
     * PATCH /users/{id}/status
     * Returns 204 on success.
     */
    updateUserStatus: builder.mutation<void, {id: string; status: number}>({
      query: ({id, status}) => ({
        url: `/users/${id}/status`,
        method: 'PATCH',
        body: {status},
      }),
      // Optionally, you can add invalidatesTags or onQueryStarted for cache updates
    }),
    /**
     * Resend invitation to user by id.
     * POST /users/{id}/resend-invitation
     * Returns 204 on success.
     * If error 429 with statusCode 429, show error message from response.
     */
    resendInvitation: builder.mutation<void, {id: string}>({
      query: ({id}) => ({
        url: `/users/${id}/resend-invitation`,
        method: 'POST',
      }),
    }),
    addRole: builder.mutation<{}, Pick<IRole, 'name' | 'permissions'>>({
      query: roleData => ({
        url: '/roles',
        method: 'POST',
        body: roleData,
      }),
    }),
    updateRole: builder.mutation<{}, Pick<IRole, 'id' | 'name' | 'permissions'>>({
      query: roleData => ({
        url: `/roles/${roleData.id}`,
        method: 'PATCH',
        body: {
          name: roleData.name,
          permissions: roleData.permissions,
        },
      }),
    }),
    activateDeactiveRole: builder.mutation<{}, {id: string; isActive: boolean}>({
      query: ({id, isActive}) => ({
        url: `/roles/${id}/`,
        method: 'PATCH',
        body: {
          status: isActive ? RoleStatus.ACTIVE : RoleStatus.INACTIVE,
        },
      }),
    }),

    getAllUserViewStatuses: builder.query<Record<UserStatus, string>, void>({
      query: () => ({
        url: '/users/all-statuses',
        method: 'GET',
      }),
    }),
  }),
});

/**
 * Exported RTK Query hooks for authentication and user management.
 */
export const {
  useLoginMutation,
  useLogoutMutation,
  useGetTokenMutation,
  useGetUserQuery,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useGetRolesByGroupQuery,
  useLazyGetRolesByGroupQuery,
  useGetCountOfRolesByGroupQuery,
  useLazyGetCountOfRolesByGroupQuery,
  useGetUserListsQuery,
  useLazyGetUserListsQuery,
  useGetUsersCountQuery,
  useLazyGetUsersCountQuery,
  useSetActivationPasswordMutation,
  useUpdateUserStatusMutation,
  useResendInvitationMutation,
  useAddRoleMutation,
  useUpdateRoleMutation,
  useActivateDeactiveRoleMutation,
  useGetAllUserViewStatusesQuery,
} = authApiSlice;
