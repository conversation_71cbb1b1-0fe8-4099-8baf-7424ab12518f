import {AnyObject} from 'yup';

export enum PlanStatus {
  ACTIVE,
  INACTIVE,
}

export type PlanType = {
  name: string;
  tier: string;
  price: number;
  allowedUnlimitedUsers?: boolean;
  costPerUser?: number | null;
  billingCycleId: string;
  currencyId: string;
  configureDeviceId: string;
  planSizeId?: string;
  metaData?: Record<string, unknown>;
  description?: string;
};

export interface PlanFilterDTO {
  limit?: number;

  offset?: number;
  order?: string;
  where?: {
    status?: {inq: number[]};
    name?: {ilike: string};
    billingCycleId?: string;
    configureDeviceId?: string;
    tier?: string;
  };
  include?: {relation: string}[];
}

export interface IBaseEntity {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string | null;
  createdBy: string;
  modifiedBy: string | null;
  id: string;
}

export interface IBillingCycle extends IBaseEntity {
  cycleName: string;
  duration: number;
  durationUnit: string;
  description: string;
}

export interface ICurrency {
  id: string;
  currencyCode: string;
  currencyName: string;
  symbol: string;
  country: string;
}

export interface IConfigureDevice extends IBaseEntity {
  min: number;
  max: number;
  computeSize: string;
  dbSize: string;
}

export interface IPlanSize extends IBaseEntity {
  size: string;
  config: Record<string, unknown>;
}

export interface IPlanHistory extends IBaseEntity {
  price: number;
  version: string;
  allowedUnlimitedUsers: boolean;
  costPerUser: number;
  planId: string;
  plan: string;
}

export type PlanResponse = IBaseEntity & {
  name: string;
  description: string | null;
  tier: string;
  size: string | null;
  price: string;
  status: string;
  metaData?: AnyObject;
  version: string;
  allowedUnlimitedUsers: boolean;
  costPerUser: string;
  billingCycleId: string;
  currencyId: string;
  configureDeviceId: string;
  planSizeId: string;
  billingCycle?: IBillingCycle;
  currency?: ICurrency;
  configureDevice?: IConfigureDevice;
  planSize?: IPlanSize;
  planHistories?: IPlanHistory[];
};

export interface IApiPlanFilter {
  status?: number[];
  billingCycleId?: string[];
  configureDeviceId?: string;
  tier?: string;
  searchValue?: string;
  order?: string;
  limit?: number;
  offset?: number;
  include?: {relation: string}[];
}

export type PlanApiDTO = IApiPlanFilter & {
  where?: AnyObject;
};
export type PlanApiForCountDTO = Omit<IApiPlanFilter, 'limit' | 'offset'>;
