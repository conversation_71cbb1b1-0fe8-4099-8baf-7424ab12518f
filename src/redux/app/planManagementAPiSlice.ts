import {ApiSliceIdentifier} from 'Constants/enums';
import {apiSlice} from 'redux/apiSlice';
import {IConfigureDevice, PlanApiDTO, PlanFilterDTO, PlanResponse, PlanType} from './types/plan.type';

const apiSliceIdentifier = ApiSliceIdentifier.TENANT_FACADE;

const buildWhere = (filter: PlanApiDTO) => {
  const andConditions: Record<string, unknown>[] = [];

  if (filter.status?.length) {
    andConditions.push({status: {inq: filter.status}});
  }

  if (filter.billingCycleId?.length) {
    andConditions.push({billingCycleId: {inq: filter.billingCycleId}});
  }

  (['configureDeviceId', 'tier'] as const).forEach(key => {
    if (filter[key]) {
      andConditions.push({[key]: filter[key]});
    }
  });

  if (filter.searchValue?.trim()) {
    const search = filter.searchValue.trim();
    andConditions.push({
      or: [{name: {ilike: `%${search}%`}}, {price: {ilike: `%${search}%`}}],
    });
  }

  if (andConditions.length === 1) {
    return andConditions[0];
  }

  return {and: andConditions};
};

const composePlanFilter = (filterQuery: PlanApiDTO): PlanFilterDTO => {
  if (!filterQuery || Object.keys(filterQuery).length === 0) return {};

  const {limit, offset, order} = filterQuery;
  const where = buildWhere(filterQuery);

  return {
    ...(limit && {limit}),
    ...(offset && {offset}),
    ...(order && {order}),
    ...(Object.keys(where).length && {where}),
    include: [
      {relation: 'billingCycle'},
      {relation: 'currency'},
      {relation: 'planSize'},
      {relation: 'planHistories'},
      {relation: 'configureDevice'},
    ],
  };
};

export const planApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getPlans: builder.query({
      query: (filterQuery: PlanApiDTO) => {
        const filter = composePlanFilter(filterQuery);
        return {
          url: '/plans',
          method: 'GET',
          params: {
            filter: JSON.stringify(filter),
          },
          apiSliceIdentifier,
        };
      },
    }),
    getPlansCount: builder.query({
      query: (filterQuery = {}) => {
        const filter = composePlanFilter(filterQuery);
        return {
          url: '/plans/count',
          method: 'GET',
          params: filter?.where && {
            where: JSON.stringify({
              ...filter.where,
            }),
          },
          apiSliceIdentifier,
        };
      },
    }),
    createPlan: builder.mutation({
      query: (planData: PlanType) => {
        return {
          url: '/plans',
          method: 'POST',
          body: planData,
          apiSliceIdentifier,
        };
      },
    }),
    getPlanById: builder.query<PlanResponse, {planId: string; filter?: object}>({
      query: ({planId, filter}) => {
        const params: Record<string, string> = {};

        if (filter) {
          params.filter = JSON.stringify(filter); // LoopBack expects filter as a JSON string
        }

        return {
          url: `/plans/${planId}`,
          method: 'GET',
          params, // <-- automatically converted into query string
          apiSliceIdentifier,
        };
      },
    }),
    updatePlanById: builder.mutation<PlanResponse, {planID: string; data: Partial<PlanType>}>({
      query: ({planID, data}) => ({
        url: `/plans/${planID}`,
        method: 'PATCH',
        body: data,
        apiSliceIdentifier,
      }),
    }),
    updateStatus: builder.mutation({
      query: ({planId, status}: {planId: string; status: number}) => {
        return {
          url: `/plans/${planId}/status`,
          method: 'PATCH',
          body: {status},
          apiSliceIdentifier,
        };
      },
    }),
    getDevices: builder.query({
      query: (filterQuery = {}) => ({
        url: '/configure-devices',
        method: 'GET',
        apiSliceIdentifier,
        params: {
          filter: JSON.stringify(filterQuery),
        },
      }),
    }),
    updateDevices: builder.mutation({
      query: (deviceData: Partial<IConfigureDevice>[]) => {
        return {
          url: '/configure-devices/upsert-batch',
          method: 'POST',
          body: deviceData,
          apiSliceIdentifier,
        };
      },
    }),
    getStatus: builder.query({
      query: () => ({
        url: '/plans/all-status',
        method: 'GET',
        apiSliceIdentifier,
      }),
    }),
    getTenures: builder.query({
      query: filter => ({
        url: '/subscriptions-tenure',
        method: 'GET',
        params: filter ? {filter: JSON.stringify(filter)} : {},
        apiSliceIdentifier,
      }),
    }),
    getCurrencies: builder.query({
      query: () => ({
        url: '/currencies',
        method: 'GET',
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const {
  useGetPlansQuery,
  useGetPlansCountQuery,
  useLazyGetPlansCountQuery,
  useLazyGetPlansQuery,
  useCreatePlanMutation,
  useGetDevicesQuery,
  useGetTenuresQuery,
  useGetCurrenciesQuery,
  useLazyGetDevicesQuery,
  useLazyGetTenuresQuery,
  useLazyGetCurrenciesQuery,
  useUpdateStatusMutation,
  useGetStatusQuery,
  useGetPlanByIdQuery,
  useUpdatePlanByIdMutation,
  useUpdateDevicesMutation,
} = planApiSlice;
