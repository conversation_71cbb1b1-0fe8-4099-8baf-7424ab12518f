import {ApiSliceIdentifier} from 'Constants/enums';
import {AnyObject} from 'Helpers/utils';
import {omit} from 'lodash';
import {apiSlice} from 'redux/apiSlice';
import {
  CreateTenantUserDto,
  IRole,
  ITenantStatus,
  ITriggerDto,
  Subscription,
  TenantApiDTO,
  TenantApiForCountDTO,
  TenantFilterDTO,
  TenantType,
  UpdateTenantUserDto,
  UserViewType,
} from './types';
import {TenantOverviewDTO} from './types/tenantOverview.dto';

const apiSliceIdentifier = ApiSliceIdentifier.TENANT_FACADE;

/**
 * Composes a tenant filter object from the provided filter query.
 *
 * @param filterQuery The filter criteria for querying tenants.
 * It can include properties like limit, offset, order, status, dateRange, and searchValue.
 * @returns A TenantFilterDTO object that can be used to filter tenant queries.
 * If no filterQuery is provided, it returns an empty object.
 */
const composeTenantFilter = (filterQuery: TenantApiDTO): TenantFilterDTO => {
  if (Object.keys(filterQuery).length === 0) return {};

  const filter: TenantFilterDTO = {
    ...pickBasicFilters(filterQuery),
  };

  const where = {
    ...filterQuery.where,
    ...buildStatusFilter(filterQuery),
    ...buildDateRangeFilter(filterQuery),
    ...buildSearchFilter(filterQuery),
  };

  if (Object.keys(where).length > 0) {
    filter.where = where;
  }

  return filter;
};

function pickBasicFilters(query: TenantApiDTO): Partial<TenantFilterDTO> {
  const {include, limit, offset, order} = query;
  return {include, limit, offset, order};
}

function buildStatusFilter(query: TenantApiDTO) {
  return query.status?.length ? {status: {inq: query.status}} : {};
}

function buildDateRangeFilter(query: TenantApiDTO) {
  if (!query.dateRange) return {};
  const {startDate, endDate} = query.dateRange;
  return {createdOn: {between: [startDate.toISOString(), endDate.toISOString()]}};
}

function buildSearchFilter(query: TenantApiDTO) {
  const search = query.searchValue?.trim();
  return search ? {name: {ilike: `%${search}%`}} : {};
}

/**
 * Injects tenant management related endpoints into the base API slice.
 *
 * @remarks
 * This slice provides endpoints for managing tenants, including fetching tenants,
 * counting tenants, verifying tenant keys, and creating new tenants.
 *
 * @example
 * // Usage in a component
 * const { data, error } = useGetTenantsQuery(filterQuery);
 *
 * @see {@link apiSlice}
 *
 * @endpoint getTenants
 * Fetches a list of tenants based on the provided filter query.
 * @param filterQuery - The filter criteria for querying tenants.
 *
 * @endpoint getTenantsCount
 * Retrieves the count of tenants matching the provided filter query.
 * @param filterQuery - (Optional) The filter criteria for counting tenants.
 *
 * @endpoint verifyTenantKey
 * Verifies a tenant key.
 * @param KeyDto - The key data transfer object to verify.
 *
 * @endpoint createTenant
 * Creates a new tenant.
 * @param formData - The form data containing tenant details.
 */
export const tenantApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getTenants: builder.query<TenantType[], TenantApiDTO>({
      query: (filterQuery: TenantApiDTO) => {
        const filter = composeTenantFilter(filterQuery);
        return {
          url: '/tenants',
          method: 'GET',
          params: {
            filter: JSON.stringify(filter),
          },
          apiSliceIdentifier,
        };
      },
    }),
    exportAllTenants: builder.query<AnyObject, void>({
      query: () => {
        return {
          url: '/tenants/export',
          method: 'GET',
          apiSliceIdentifier,
          noBaseErrorHandling: true,
        };
      },
    }),
    getSubscription: builder.query<Subscription[], object>({
      query: (filterQuery: object) => {
        return {
          url: '/subscriptions',
          method: 'GET',
          params: {
            filter: JSON.stringify(filterQuery),
          },
          apiSliceIdentifier,
        };
      },
    }),
    getTenantsCount: builder.query({
      query: (filterQuery: TenantApiForCountDTO = {}) => {
        const filter = composeTenantFilter(filterQuery);
        return {
          url: '/tenants/count',
          method: 'GET',
          params: filter?.where && {
            where: JSON.stringify({
              ...filter.where,
            }),
          },
          apiSliceIdentifier,
        };
      },
    }),
    verifyTenantKey: builder.mutation({
      query: KeyDto => ({
        url: '/tenants/verify-key',
        method: 'POST',
        body: KeyDto,
        apiSliceIdentifier,
      }),
    }),
    validateUser: builder.query({
      query: ({email, userName}) => ({
        url: '/tenants/validate',
        method: 'GET',
        params: {email, userName},
        apiSliceIdentifier,
      }),
    }),
    createTenant: builder.mutation<TenantType, FormData>({
      query: formData => ({
        url: '/tenants',
        method: 'POST',
        apiSliceIdentifier,
        body: formData,
      }),
    }),
    getAllTenantStatuses: builder.query<ITenantStatus, void>({
      query: () => ({
        url: '/tenants/all-statuses',
        method: 'GET',
        apiSliceIdentifier,
      }),
    }),
    getTenantById: builder.query<TenantType, string>({
      query: (tenantId: string) => ({
        url: `/tenants/${tenantId}`,
        method: 'GET',
        apiSliceIdentifier,
      }),
    }),
    retryProvisioning: builder.mutation({
      query: ({tenantId}) => ({
        url: `/tenants/${tenantId}/provision`,
        method: 'POST',
        apiSliceIdentifier,
      }),
    }),
    reActivateTenant: builder.mutation({
      query: ({tenantId}) => ({
        url: `/tenants/${tenantId}/reactivate`,
        method: 'POST',
        apiSliceIdentifier,
      }),
    }),
    deProvisioning: builder.mutation<TenantType, {tenantId: string}>({
      query: ({tenantId}) => ({
        url: `/tenants/${tenantId}/de-provision`,
        method: 'POST',
        apiSliceIdentifier,
      }),
    }),

    reTrigger: builder.mutation<TenantType, {tenantId: string; body: ITriggerDto}>({
      query: ({tenantId, body}) => ({
        url: `/tenants/${tenantId}/re-trigger`,
        method: 'POST',
        body,
        apiSliceIdentifier,
      }),
    }),

    reSendInvite: builder.mutation<TenantType, {tenantId: string}>({
      query: ({tenantId}) => ({
        url: `/tenants/${tenantId}/send-invite`,
        method: 'POST',
        apiSliceIdentifier,
      }),
    }),
    updateTenantById: builder.mutation<TenantType, {id: string; body: FormData}>({
      query: ({body, id}) => ({
        url: `/tenants/${id}/edit`,
        method: 'PATCH',
        apiSliceIdentifier,
        body,
      }),
    }),

    getUserById: builder.query<UserViewType[], string>({
      query: id => {
        const filter = {where: {userTenantId: id}};
        return {
          url: '/users',
          method: 'GET',
          params: {filter: JSON.stringify(filter)},
        };
      },
    }),
    getUserByEmail: builder.query<UserViewType[], string>({
      query: email => {
        const filter = {where: {email}};
        return {
          url: '/users',
          method: 'GET',
          params: {filter: JSON.stringify(filter)},
        };
      },
    }),
    getRoles: builder.query<IRole[], {order: string} | void>({
      query: params => ({
        url: '/roles',
        method: 'GET',
        params: {
          ...(params && {filter: JSON.stringify({order: params.order})}),
        },
      }),
    }),
    getRoleByName: builder.query<IRole[], string | {name: string; ignoreCase: boolean}>({
      query: roleName => {
        let filter: AnyObject;
        if (typeof roleName !== 'string' && roleName.ignoreCase) {
          filter = {where: {name: {ilike: roleName.name}}};
        } else {
          filter = {where: {name: roleName}};
        }

        return {
          url: '/roles',
          method: 'GET',
          params: {
            filter: JSON.stringify(filter),
          },
        };
      },
    }),
    updateUser: builder.mutation<unknown, Partial<UpdateTenantUserDto>>({
      query: body => ({
        url: `/users/${body.userId}`,
        method: 'PATCH',
        body: omit(body, 'userId'),
      }),
    }),
    addUser: builder.mutation<unknown, CreateTenantUserDto[]>({
      query: body => ({
        url: `/users/bulk`,
        method: 'POST',
        body: body,
      }),
    }),
    getRoleById: builder.query<IRole[], {roleID: string; includeAll?: boolean}>({
      query: obj => ({
        url: `/roles${obj.includeAll ? '/all' : ''}`,
        method: 'GET',
        params: {
          filter: JSON.stringify({where: {id: obj.roleID}}),
        },
      }),
    }),
    getTenantStatusMetrics: builder.query<TenantOverviewDTO, TenantApiDTO>({
      query: (filterQuery: TenantApiDTO) => {
        const filter = composeTenantFilter(filterQuery);
        return {
          url: `/tenants/dashboard-metrics`,
          method: 'GET',
          params: {
            filter: JSON.stringify(filter),
          },
          apiSliceIdentifier,
        };
      },
    }),
  }),
});

export const {
  useVerifyTenantKeyMutation,
  useGetTenantsQuery,
  useGetTenantsCountQuery,
  useLazyGetTenantsCountQuery,
  useLazyGetTenantsQuery,
  useCreateTenantMutation,
  useGetAllTenantStatusesQuery,
  useGetTenantByIdQuery,
  useLazyGetTenantByIdQuery,
  useGetUserByIdQuery,
  useLazyGetUserByEmailQuery,
  useLazyGetUserByIdQuery,
  useGetRolesQuery,
  useLazyGetRolesQuery,
  useUpdateUserMutation,
  useAddUserMutation,
  useGetRoleByIdQuery,
  useLazyGetRoleByIdQuery,
  useGetSubscriptionQuery,
  useUpdateTenantByIdMutation,
  useGetRoleByNameQuery,
  useLazyGetRoleByNameQuery,
  useGetTenantStatusMetricsQuery,
  useRetryProvisioningMutation,
  useDeProvisioningMutation,
  useReSendInviteMutation,
  useReActivateTenantMutation,
  useReTriggerMutation,
  useValidateUserQuery,
  useLazyValidateUserQuery,
  useLazyExportAllTenantsQuery,
} = tenantApiSlice;
