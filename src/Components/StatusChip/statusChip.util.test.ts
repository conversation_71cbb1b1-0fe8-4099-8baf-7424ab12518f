import {testingPalette, useThemeColor} from 'TestHelper/TestHelper';
import {StatusChipState, getFontColor, getIndicatorColor, getStatusColor, getStatusLabel} from './statusChip.util';

// Mock colors import for fallback
vi.mock('Providers/theme/colors', () => ({
  colors: {white: '#FFFFFF'},
}));

const UNKNOWN_RANDOM_STATUS = 999;
const UNKNOWN_STATUS = UNKNOWN_RANDOM_STATUS as StatusChipState;
const FallbackColorForUnknownMsg = 'returns fallback color for unknown status';
describe('StatusChip Utils', () => {
  describe('getStatusColor', () => {
    it('returns correct color for each status', () => {
      expect(useThemeColor(getStatusColor(StatusChipState.ACTIVE))).toBe(testingPalette.alert.success.bg);
      expect(useThemeColor(getStatusColor(StatusChipState.PENDINGPROVISION))).toBe(testingPalette.alert.warning.bg);
      expect(useThemeColor(getStatusColor(StatusChipState.INACTIVE))).toBe(testingPalette.alert.error.bg);
      expect(useThemeColor(getStatusColor(StatusChipState.PROVISIONFAILED))).toBe(
        testingPalette.tenantStatus.failed.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.PROVISIONING))).toBe(
        testingPalette.tenantStatus.provisioning.bg,
      );
      expect(useThemeColor(getStatusColor(StatusChipState.PENDINGONBOARDING))).toBe(testingPalette.alert.info.bg);
    });

    it(FallbackColorForUnknownMsg, () => {
      expect(useThemeColor(getStatusColor(UNKNOWN_STATUS))).toBe(testingPalette.white.main);
    });
  });

  describe('getFontColor', () => {
    it('returns correct font color for each status', () => {
      expect(useThemeColor(getFontColor(StatusChipState.ACTIVE))).toBe(testingPalette.alert.success.onBg);
      expect(useThemeColor(getFontColor(StatusChipState.PENDINGPROVISION))).toBe(testingPalette.alert.warning.onBg);
      expect(useThemeColor(getFontColor(StatusChipState.INACTIVE))).toBe(testingPalette.alert.error.onBg);
      expect(useThemeColor(getFontColor(StatusChipState.PROVISIONFAILED))).toBe(
        testingPalette.tenantStatus.failed.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.PROVISIONING))).toBe(
        testingPalette.tenantStatus.provisioning.onBg,
      );
      expect(useThemeColor(getFontColor(StatusChipState.PENDINGONBOARDING))).toBe(testingPalette.alert.info.onBg);
    });

    it(FallbackColorForUnknownMsg, () => {
      expect(useThemeColor(getFontColor(UNKNOWN_STATUS))).toBe(testingPalette.white.main);
    });
  });

  describe('getIndicatorColor', () => {
    it('returns correct indicator color for each status', () => {
      expect(useThemeColor(getIndicatorColor(StatusChipState.ACTIVE))).toBe(testingPalette.alert.success.main);
      expect(useThemeColor(getIndicatorColor(StatusChipState.PENDINGPROVISION))).toBe(
        testingPalette.alert.warning.main,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.INACTIVE))).toBe(testingPalette.alert.error.main);
      expect(useThemeColor(getIndicatorColor(StatusChipState.PROVISIONFAILED))).toBe(
        testingPalette.tenantStatus.failed.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.PROVISIONING))).toBe(
        testingPalette.tenantStatus.provisioning.indicator,
      );
      expect(useThemeColor(getIndicatorColor(StatusChipState.PENDINGONBOARDING))).toBe(
        testingPalette.alert.warning.main,
      );
    });

    it(FallbackColorForUnknownMsg, () => {
      expect(useThemeColor(getIndicatorColor(UNKNOWN_STATUS))).toBe(testingPalette.white.main);
    });
  });

  describe('getStatusLabel', () => {
    it('returns correct label for each status', () => {
      expect(getStatusLabel(StatusChipState.ACTIVE)).toBe('Active');
      expect(getStatusLabel(StatusChipState.PENDINGPROVISION)).toBe('Pending Provision');
      expect(getStatusLabel(StatusChipState.INACTIVE)).toBe('Inactive');
      expect(getStatusLabel(StatusChipState.PROVISIONFAILED)).toBe('Failed Provision');
      expect(getStatusLabel(StatusChipState.PROVISIONING)).toBe('Provisioning');
      expect(getStatusLabel(StatusChipState.PENDINGONBOARDING)).toBe('Pending Onboarding');
    });

    it('returns empty string for unknown status', () => {
      expect(getStatusLabel(UNKNOWN_STATUS)).toBe('');
    });
  });
});
