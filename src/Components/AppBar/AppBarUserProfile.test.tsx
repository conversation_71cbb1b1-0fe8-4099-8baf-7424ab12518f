import {fireEvent, render, screen} from '@testing-library/react';
import {AnyObject} from 'Helpers/utils';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {Mock, vi} from 'vitest';
import {AppBarUserProfile} from './AppBarUserProfile';

// Mock dependencies
vi.mock('redux/auth/authApiSlice', () => ({
  useGetUserQuery: vi.fn(),
}));

vi.mock('Components/ToolTipTypograpy /ToolTipTypograpy', () => ({
  __esModule: true,
  default: ({message}: {message: string}) => <span>{message}</span>,
}));
vi.mock('./PopoverWithArrow', () => ({
  PopoverWithArrow: ({open, children}: AnyObject) => (open ? <div data-testid="popover">{children}</div> : null), // NOSONAR
}));

describe('AppBarUserProfile', () => {
  const mockOnLogout = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with default user when no user data', () => {
    (useGetUserQuery as Mock).mockReturnValue({data: undefined});
    render(<AppBarUserProfile onLogout={mockOnLogout} />);
    expect(screen.queryByText('User')).not.toBeInTheDocument();
    expect(screen.queryByTestId('popover')).not.toBeInTheDocument();
  });

  it('renders with user data', () => {
    (useGetUserQuery as Mock).mockReturnValue({
      data: {firstName: 'John', lastName: 'Doe', email: '<EMAIL>'},
    });
    render(<AppBarUserProfile onLogout={mockOnLogout} />);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('opens and closes popover on click', () => {
    (useGetUserQuery as Mock).mockReturnValue({
      data: {firstName: 'John', lastName: 'Doe', email: '<EMAIL>'},
    });
    render(<AppBarUserProfile onLogout={mockOnLogout} />);
    const menuButton = screen.getByTestId('menu-button');
    fireEvent.click(menuButton);
    expect(screen.getByTestId('popover')).toBeInTheDocument();
    // Close by clicking Logout
    fireEvent.click(screen.getByText('Logout'));
    expect(mockOnLogout).toHaveBeenCalled();
  });

  it('calls onLogout when Logout is clicked', () => {
    (useGetUserQuery as Mock).mockReturnValue({
      data: {firstName: 'John', lastName: 'Doe', email: '<EMAIL>'},
    });
    render(<AppBarUserProfile onLogout={mockOnLogout} />);
    const menuButton = screen.getByTestId('menu-button');
    fireEvent.click(menuButton);
    fireEvent.click(screen.getByText('Logout'));
    expect(mockOnLogout).toHaveBeenCalledTimes(1);
  });

  it('renders user profile image and arrow down', () => {
    (useGetUserQuery as Mock).mockReturnValue({
      data: {firstName: 'John', lastName: 'Doe', email: '<EMAIL>'},
    });
    render(<AppBarUserProfile onLogout={mockOnLogout} />);
    expect(screen.getByAltText('User Profile')).toBeInTheDocument();
    expect(screen.getByAltText('Arrow Down')).toBeInTheDocument();
  });
});
