import {Typography, useTheme} from '@mui/material';
import {DefaultToolTip} from 'Components/ToolTipTypography/ToolTipTypography';
import React, {useEffect, useRef, useState} from 'react';

type EllipsisTextProps = {
  text: string;
  sx?: object; // allow overriding/adding custom styles
  'data-testid'?: string;
};

const EllipsisText: React.FC<EllipsisTextProps> = ({text, sx = {}, 'data-testid': dataTestId}) => {
  const theme = useTheme();
  const textRef = useRef<HTMLSpanElement>(null);
  const [isOverflowed, setIsOverflowed] = useState(false);

  useEffect(() => {
    const el = textRef.current;
    if (el) {
      setIsOverflowed(el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth);
    }
  }, [text]);

  const content = (
    <Typography
      ref={textRef}
      sx={{
        fontSize: '0.8125rem',
        color: theme.palette.text.primary,
        fontWeight: 500,
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        display: '-webkit-box',
        WebkitLineClamp: 1,
        WebkitBoxOrient: 'vertical',
        wordBreak: 'break-word',
        ...sx, // merge/override with user-provided sx
      }}
      data-testid={dataTestId}
    >
      {text || 'N/A'}
    </Typography>
  );

  return <>{isOverflowed ? <DefaultToolTip title={text}>{content}</DefaultToolTip> : content}</>;
};

export default EllipsisText;
