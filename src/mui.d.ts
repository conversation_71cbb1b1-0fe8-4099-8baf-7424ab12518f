import '@mui/material/styles/createPalette';

declare module '@mui/material/styles/createPalette' {
  type BodyColor = SimplePaletteColorOptions & {
    dark: string;
  };
  type WhiteVariant = SimplePaletteColorOptions & {
    primary: string;
  };

  interface TypeBorder {}
  interface AlertColors {
    main: string;
    bg: string;
    onBg: string;
    border: string;
  }
  interface Alert {
    success: AlertColors;
    error: AlertColors;
    warning: AlertColors;
    info: AlertColors;
  }

  type SideNavColor = {
    active: string;
    hover: string;
    linkTextActive: string;
  };
  type ErrorType = {
    main: string;
    bg: string;
    onBg: string;
    border: string;
    lightBg: string;
    100: string;
  };

  type SuccessType = {
    main: string;
    bg: string;
    onBg: string;
    border: string;
  };

  interface TenantStatusColor {
    bg: string;
    indicator: string;
    onBg: string;
  }

  interface GradientColor {
    1: string;
    2: string;
  }

  interface PlanGradient {
    normal: GradientColor;
    selected: GradientColor;
  }

  interface TenantStatus {
    failed: TenantStatusColor;
    provisioning: TenantStatusColor;
  }

  interface Extra {
    disabledInputBg: string;
  }
  interface Palette {
    body: BodyColor;
    border: TypeBorder;
    white: WhiteVariant;
    black: PaletteColor;
    table: Table;
    sideNav: SideNavColor;
    alert: Alert;
    tenantStatus: TenantStatus;
    plan: PlanGradient;
    extra: Extra;
  }

  interface PaletteOptions {
    border?: Partial<TypeBorder>;
    body?: Partial<BodyColor>;
    white?: Partial<WhiteVariant>;
    black?: Partial<PaletteColor>;
    table?: Partial<Table>;
    sideNav?: Partial<SideNavColor>;
    alert?: Partial<Alert>;
    tenantStatus?: Partial<TenantStatus>;
    plan?: Partial<PlanGradient>;
    extra?: Partial<Extra>;
  }

  interface Table {
    header?: string;
    accordianBg?: string;
  }

  interface PaletteColor {
    50?: string;
    100?: string;
    150?: string;
    200?: string;
    300?: string;
    400?: string;
    500?: string;
    600?: string;
    700?: string;
    800?: string;
    900?: string;
  }

  interface SimplePaletteColorOptions {
    25?: string;
    50?: string;
    100?: string;
    150?: string;
    200?: string;
    300?: string;
    350?: string;
    400?: string;
    500?: string;
    600?: string;
    650?: string;
    700?: string;
    800?: string;
    900?: string;
  }
}
