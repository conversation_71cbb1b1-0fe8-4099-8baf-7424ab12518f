import {SvgIcon, SvgIconProps} from '@mui/material';

const ViewAllInvoicesIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon {...props} viewBox="0 0 14 15">
      <path
        d="M8.33317 1.01367V3.76737C8.33317 4.14073 8.33317 4.32742 8.40583 4.47003C8.46975 4.59547 8.57173 4.69745 8.69718 4.76137C8.83978 4.83403 9.02647 4.83403 9.39984 4.83403H12.1535M9.6665 8.16732H4.33317M9.6665 10.834H4.33317M5.6665 5.50065H4.33317M8.33317 0.833984H4.8665C3.7464 0.833984 3.18635 0.833984 2.75852 1.05197C2.3822 1.24372 2.07624 1.54968 1.88449 1.926C1.6665 2.35383 1.6665 2.91388 1.6665 4.03398V10.9673C1.6665 12.0874 1.6665 12.6475 1.88449 13.0753C2.07624 13.4516 2.3822 13.7576 2.75852 13.9493C3.18635 14.1673 3.7464 14.1673 4.8665 14.1673H9.13317C10.2533 14.1673 10.8133 14.1673 11.2412 13.9493C11.6175 13.7576 11.9234 13.4516 12.1152 13.0753C12.3332 12.6475 12.3332 12.0874 12.3332 10.9673V4.83398L8.33317 0.833984Z"
        stroke="#6B6B6B"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </SvgIcon>
  );
};

export default ViewAllInvoicesIcon;
