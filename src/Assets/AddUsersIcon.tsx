import {SvgIcon, SvgIconProps} from '@mui/material';

const AddUsersIcon = (
  props: SvgIconProps & {
    disabled?: boolean;
  },
) => {
  const strokeColor = props.disabled ? '#DBDBDB' : '#852352';
  const plusColor = props.disabled ? '#BABABA' : 'white';
  return (
    <SvgIcon
      {...props}
      viewBox="0 0 38 38"
      sx={{...props.sx, fontSize: '2.375rem', ...(props.disabled ? {color: 'white.main'} : {})}}
    >
      <rect x="0.5" y="0.5" width="37" height="37" rx="18.5" fill="#852352" />
      <rect x="0.5" y="0.5" width="37" height="37" rx="18.5" stroke={strokeColor} />
      <path
        d="M19 14V23.1667M14.4166 18.5833H23.5833"
        stroke={plusColor}
        strokeWidth="1.6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </SvgIcon>
  );
};

export default AddUsersIcon;
