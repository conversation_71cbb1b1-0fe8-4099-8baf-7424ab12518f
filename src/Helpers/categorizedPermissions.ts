import PermissionKey from 'Constants/enums/permissions';

/**
 * Enum representing logical groupings of permissions.
 */
export enum PermissionCategory {
  Tenant = 'Tenant',
  PendingTenant = 'Pending Tenant',
  BillingInvoices = 'Billing & Invoices',
  Plans = 'Plans',
  UserManagement = 'User Management',
  Role = 'Role Management',
}
/**
 * Maps permission categories to their respective permissions.
 * Each permission contains a label and a key from PermissionKey.
 *
 * PermissionCategory: Enum representing logical groupings of permissions.
 * categorizedPermissions: Object mapping each category to an array of permissions.
 */

export interface CategoryPermission {
  label: string;
  key: string;
  permission: string;
  dependent: string[];
}

//
/**
 * Alias for the `PermissionKey` constant, used to reference permission keys throughout the application.
 *
 * @see PermissionKey
 */
const PK = PermissionKey;

/**
 * A mapping of permission categories to their respective permissions and dependencies.
 *
 * Each category (e.g., Tenant, BillingInvoices, Plans, UserManagement, Role) contains an array of permissions,
 * where each permission object includes:
 * - `label`: A human-readable name for the permission action.
 * - `key`: A unique string identifier for the permission.
 * - `permission`: The specific permission key required for the action.
 * - `dependent`: An array of permission keys that must also be granted for this permission to be effective.
 *
 * This structure is used to organize and manage permissions in a categorized manner, supporting dependency checks
 * and UI rendering for permission management.
 */
const categorizedPermissions: Record<PermissionCategory, CategoryPermission[]> = {
  [PermissionCategory.Tenant]: [
    {
      label: 'Create',
      key: 'tenant:create',
      permission: PK.CreateTenant,
      dependent: [PK.ViewTenant],
    },
    {
      label: 'Edit',
      key: 'tenant:edit',
      permission: PK.UpdateTenant,
      dependent: [PK.ViewTenant],
    },
    {label: 'View', key: 'tenant:view', permission: PK.ViewTenant, dependent: [PK.ViewPlan]},
    {
      label: 'Delete',
      key: 'tenant:delete',
      permission: PK.DeleteTenant,
      dependent: [PK.ViewTenant],
    },
  ],
  [PermissionCategory.PendingTenant]: [
    {
      label: 'Create',
      key: 'pendingTenant:create',
      permission: PK.CreateLead,
      dependent: [PK.ViewLead],
    },
    {
      label: 'Edit',
      key: 'pendingTenant:edit',
      permission: PK.UpdateLead,
      dependent: [PK.ViewLead],
    },
    {label: 'View', key: 'pendingTenant:view', permission: PK.ViewLead, dependent: []},
  ],
  [PermissionCategory.BillingInvoices]: [{label: 'View', key: 'bill:view', permission: PK.ViewInvoice, dependent: []}],
  [PermissionCategory.Plans]: [
    {label: 'Create', key: 'plan:create', permission: PK.CreatePlan, dependent: [PK.ViewPlan]},
    {label: 'Edit', key: 'plan:edit', permission: PK.UpdatePlan, dependent: [PK.ViewPlan]},
    {label: 'View', key: 'plan:view', permission: PK.ViewPlan, dependent: []},
    {
      label: 'Update Device Limits',
      key: 'plan:update-device-limits',
      permission: PK.UpdateConfigureDevices,
      dependent: [PK.ViewPlan],
    },
    {
      label: 'View Device Limits',
      key: 'plan:view-device-limits',
      permission: PK.ViewConfigureDevices,
      dependent: [PK.ViewPlan],
    },
  ],
  [PermissionCategory.UserManagement]: [
    {
      label: 'Create',
      key: 'user:create',
      permission: PK.CreateTenantUser,
      dependent: [PK.ViewTenantUser, PK.ViewRoles],
    },
    {label: 'Edit', key: 'user:edit', permission: PK.UpdateTenantUser, dependent: [PK.ViewTenantUser]},
    {label: 'View', key: 'user:view', permission: PK.ViewTenantUser, dependent: []},
  ],
  [PermissionCategory.Role]: [
    {label: 'Create', key: 'role:create', permission: PK.AddRole, dependent: [PK.ViewRoles]},
    {label: 'Edit', key: 'role:edit', permission: PK.UpdateRole, dependent: [PK.ViewRoles]},
    {label: 'View', key: 'role:view', permission: PK.ViewRoles, dependent: []},
  ],
};

export default categorizedPermissions;
