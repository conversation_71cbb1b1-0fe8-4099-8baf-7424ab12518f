export enum Integers {
  // 0 to 100 (every number)
  <PERSON> = 0,
  One = 1,
  Two = 2,
  <PERSON> = 3,
  <PERSON> = 4,
  <PERSON> = 5,
  <PERSON> = 6,
  <PERSON> = 7,
  Eight = 8,
  <PERSON> = 9,
  Ten = 10,
  Eleven = 11,
  Twelve = 12,
  Thirteen = 13,
  Fourteen = 14,
  Fifteen = 15,
  Sixteen = 16,
  <PERSON><PERSON> = 17,
  <PERSON>een = 18,
  <PERSON><PERSON> = 19,
  Twenty = 20,
  TwentyOne = 21,
  TwentyTwo = 22,
  TwentyThree = 23,
  TwentyFour = 24,
  TwentyFive = 25,
  TwentySix = 26,
  TwentySeven = 27,
  TwentyEight = 28,
  TwentyNine = 29,
  Thirty = 30,
  ThirtyOne = 31,
  ThirtyTwo = 32,
  ThirtyThree = 33,
  ThirtyFour = 34,
  ThirtyFive = 35,
  ThirtySix = 36,
  ThirtySeven = 37,
  ThirtyEight = 38,
  ThirtyNine = 39,
  Forty = 40,
  FortyOne = 41,
  FortyTwo = 42,
  FortyThree = 43,
  FortyFour = 44,
  FortyFive = 45,
  FortySix = 46,
  FortySeven = 47,
  FortyEight = 48,
  FortyNine = 49,
  Fifty = 50,
  FiftyOne = 51,
  FiftyTwo = 52,
  FiftyThree = 53,
  FiftyFour = 54,
  FiftyFive = 55,
  FiftySix = 56,
  FiftySeven = 57,
  FiftyE<PERSON> = 58,
  FiftyN<PERSON> = 59,
  <PERSON><PERSON> = 60,
  <PERSON><PERSON><PERSON><PERSON> = 61,
  <PERSON><PERSON><PERSON><PERSON> = 62,
  Sixty<PERSON>hree = 63,
  <PERSON>ty<PERSON><PERSON> = 64,
  <PERSON><PERSON>F<PERSON> = 65,
  <PERSON><PERSON><PERSON>ix = 66,
  SixtySeven = 67,
  SixtyEight = 68,
  SixtyNine = 69,
  Seventy = 70,
  SeventyOne = 71,
  SeventyTwo = 72,
  SeventyThree = 73,
  SeventyFour = 74,
  SeventyFive = 75,
  SeventySix = 76,
  SeventySeven = 77,
  SeventyEight = 78,
  SeventyNine = 79,
  Eighty = 80,
  EightyOne = 81,
  EightyTwo = 82,
  EightyThree = 83,
  EightyFour = 84,
  EightyFive = 85,
  EightySix = 86,
  EightySeven = 87,
  EightyEight = 88,
  EightyNine = 89,
  Ninety = 90,
  NinetyOne = 91,
  NinetyTwo = 92,
  NinetyThree = 93,
  NinetyFour = 94,
  NinetyFive = 95,
  NinetySix = 96,
  NinetySeven = 97,
  NinetyEight = 98,
  NinetyNine = 99,
  OneHundred = 100,
  OneTwentyEight = 128,

  // From 150 to 1000 (step of 50)
  OneHundredFifty = 150,
  TwoHundred = 200,
  TwoHundredFifty = 250,
  TwoHundredFiftyFour = 254,
  ThreeHundred = 300,
  ThreeHundredFifty = 350,
  FourHundred = 400,
  FourHundredOne = 401,
  FourHundredFifty = 450,
  FiveHundred = 500,
  FiveHundredFifty = 550,
  SixHundred = 600,
  SixHundredFifty = 650,
  SevenHundred = 700,
  SevenHundredFifty = 750,
  EightHundred = 800,
  EightHundredFifty = 850,
  NineHundred = 900,
  NineHundredFifty = 950,
  OneThousand = 1000,
  TwelveHundred = 1200,
  FourThousand = 4000,
  FourHundredTen = 410,
}
