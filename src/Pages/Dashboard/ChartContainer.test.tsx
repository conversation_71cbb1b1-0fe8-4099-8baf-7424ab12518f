// src/Pages/Dashboard/ChartContainer.test.tsx
import {screen} from '@testing-library/react';
import {renderWithTheme} from 'TestHelper/TestHelper';
import ChartContainer from './ChartContainer';

describe('ChartContainer', () => {
  it('renders the title and children', () => {
    renderWithTheme(
      <ChartContainer title="Test Chart">
        <div data-testid="chart-child">Chart Content</div>
      </ChartContainer>,
    );
    expect(screen.getByText('Test Chart')).toBeInTheDocument();
    expect(screen.getByTestId('chart-child')).toBeInTheDocument();
    expect(screen.getByTestId('chart-child')).toHaveTextContent('Chart Content');
  });
});
