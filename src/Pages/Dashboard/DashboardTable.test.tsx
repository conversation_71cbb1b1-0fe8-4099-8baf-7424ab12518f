// src/Pages/Dashboard/DashboardTable.test.tsx

import {fireEvent, screen} from '@testing-library/react';
import {memoryRenderWithTheme} from 'TestHelper/TestHelper';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import DashboardTable from './DashboardTable';

const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

vi.mock('Components/PermissionRedirectWrapper/PermissionProvider', () => ({
  usePermissions: () => ({
    hasPermission: () => true,
  }),
}));

vi.mock('Components/PermissionWrapper', () => ({
  __esModule: true,
  default: ({children}: {children: React.ReactNode}) => <>{children}</>,
}));

vi.mock('Components/Table', () => ({
  Table: ({data, columns, tablePropsObject = {}, ...props}: any) => (
    <table data-testid="mock-table">
      <tbody>
        {data.map((row: any, idx: number) => (
          <tr key={idx} data-testid="mock-row" onClick={() => tablePropsObject.bodyRowProps?.onClickRow?.(idx)}>
            {columns.map((col: any) => (
              <td key={col.accessorKey || col.header}>{row[col.accessorKey]}</td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  ),
}));

describe('DashboardTable', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const tenants = [
    {
      id: '1',
      name: 'Tenant One',
      status: 0,
      key: 'TENANT1',
      tenantDetails: {id: '1', name: 'Tenant One', key: 'TENANT1', status: 0},
      domains: [],
      addressId: '',
      deleted: false,
      deletedOn: '',
      deletedBy: '',
      createdOn: '',
      modifiedOn: '',
      createdBy: '',
      modifiedBy: '',
      lang: 'en',
      planName: 'Basic',
      subscriptions: [],
      files: [],
      contact: {email: '<EMAIL>'},
    },
    {
      id: '2',
      name: 'Tenant Two',
      status: 0,
      key: 'TENANT2',
      tenantDetails: {id: '2', name: 'Tenant Two', key: 'TENANT2', status: 0},
      domains: [],
      addressId: '',
      deleted: false,
      deletedOn: '',
      deletedBy: '',
      createdOn: '',
      modifiedOn: '',
      createdBy: '',
      modifiedBy: '',
      lang: 'en',
      planName: 'Basic',
      subscriptions: [],
      files: [],
      contact: {email: '<EMAIL>'},
    },
  ];

  it('renders title and "View all" link', () => {
    memoryRenderWithTheme(<DashboardTable tenants={tenants} />);
    expect(screen.getByText('Recent Tenants')).toBeInTheDocument();
    expect(screen.getByText('View all')).toBeInTheDocument();
  });

  it('renders tenant rows', () => {
    memoryRenderWithTheme(<DashboardTable tenants={tenants} />);
    expect(screen.getAllByTestId('mock-row').length).toBe(2);
    expect(screen.getByText('Tenant One')).toBeInTheDocument();
    expect(screen.getByText('Tenant Two')).toBeInTheDocument();
  });

  it('navigates to tenants page on "View all" click', () => {
    memoryRenderWithTheme(<DashboardTable tenants={tenants} />);
    fireEvent.click(screen.getByText('View all'));
    expect(mockNavigate).toHaveBeenCalled();
  });

  it('navigates to tenant details on row click', () => {
    memoryRenderWithTheme(<DashboardTable tenants={tenants} />);
    fireEvent.click(screen.getAllByTestId('mock-row')[0]);
    expect(mockNavigate).toHaveBeenCalled();
  });

  it('renders empty table if no tenants', () => {
    memoryRenderWithTheme(<DashboardTable tenants={[]} />);
    expect(screen.getByTestId('mock-table')).toBeInTheDocument();
  });
});
