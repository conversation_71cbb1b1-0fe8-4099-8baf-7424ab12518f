import {Box, SxProps} from '@mui/material';
import {Integers} from 'Helpers/integers';
import {headerTypographyProps} from './styles';

interface ChartContainerProps {
  children: React.ReactNode;
  title: string;
  sx?: SxProps;
}

/**
 * A container component for displaying a chart with a title and custom styling.
 *
 * @param {ChartContainerProps} props - The props for the ChartContainer component.
 * @param {React.ReactNode} props.children - The chart or content to be displayed inside the container.
 * @param {string} props.title - The title displayed above the chart.
 * @param {SxProps<Theme>} [props.sx] - Optional custom styles to apply to the container.
 *
 * @returns {JSX.Element} The rendered ChartContainer component.
 */
const ChartContainer = ({children, title, sx}: ChartContainerProps) => {
  return (
    <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'start', width: {xs: '100%', md: '50%'}}}>
      <Box sx={{...headerTypographyProps}}>{title}</Box>
      <Box
        width={'100%'}
        sx={{
          border: theme => `1px solid ${theme.palette.body[Integers.OneHundred]}`,
          borderRadius: '0.625rem',
          overflowX: 'auto',
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default ChartContainer;

ChartContainer.displayName = 'ChartContainer';
