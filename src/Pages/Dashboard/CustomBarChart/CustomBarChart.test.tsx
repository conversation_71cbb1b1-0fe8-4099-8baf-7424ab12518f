// src/Pages/Dashboard/CustomBarChart/CustomBarChart.test.tsx
import {fireEvent, screen, waitFor} from '@testing-library/react';
import {renderWithTheme} from 'TestHelper/TestHelper';
import CustomBarChart, {IBarChartData} from './CustomBarChart';

const mockData: IBarChartData[] = [
  {name: 'A', value: 30, color: '#8884d8', tooltip: 'Alpha'},
  {name: 'B', value: 20, color: '#82ca9d', tooltip: 'Beta'},
];

describe('CustomBarChart', () => {
  it('renders no data view when data is empty', () => {
    renderWithTheme(<CustomBarChart data={[]} height={300} />);
    expect(screen.getByText('No record to display')).toBeInTheDocument();
  });

  it('shows tooltip on hover', async () => {
    renderWithTheme(<CustomBarChart data={mockData} height={300} />);
    // Try to find any SVG element and simulate mouse over
    const svg = document.querySelector('svg');
    if (svg) {
      fireEvent.mouseOver(svg);
      await waitFor(() => {
        expect(screen.getByText('Alpha')).toBeInTheDocument();
      });
    }
  });

  it('calls onClick handler when bar is clicked', async () => {
    const handleClick = vi.fn();
    renderWithTheme(<CustomBarChart data={mockData} height={300} onClick={handleClick} />);
    // Try to find any SVG element and simulate click
    const svg = document.querySelector('svg');
    if (svg) {
      fireEvent.click(svg);
      expect(handleClick).toHaveBeenCalled();
    }
  });
});
