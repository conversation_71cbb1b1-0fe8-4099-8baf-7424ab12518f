import {Box, Typography, useTheme} from '@mui/material';
import {Integers} from 'Helpers/integers';
import {AnyObject, defaultFormatNumber} from 'Helpers/utils';
import React, {useCallback} from 'react';
import {Bar, BarChart, Cell, ResponsiveContainer, Tooltip, TooltipProps, XAxis, YAxis} from 'recharts';
import {CategoricalChartProps} from 'recharts/types/chart/generateCategoricalChart';
import {NameType, ValueType} from 'recharts/types/component/DefaultTooltipContent';

export interface IBarChartData {
  name: string;
  value: number;
  color: string;
  tooltip?: string;
  tag?: AnyObject;
}
interface IBarChart {
  data: IBarChartData[];
  width?: number | string;
  height?: number | string;
  onClick?: (data: IBarChartData) => void;
}

export const CustomTooltip = (props: TooltipProps<ValueType, NameType>) => {
  const {active, payload} = props;
  if (active && payload?.length) {
    const {name, value, color, tooltip} = payload[0].payload;
    const boxSize = '0.5rem';
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: 'white.main',
          alignItems: 'flex-start',
          p: 1,
          borderRadius: '0.5rem',
          boxShadow: theme => theme.shadows[1],
          gap: 0,
        }}
      >
        <Box
          display={'flex'}
          flexDirection={'row'}
          alignContent={'center'}
          gap={0.5}
          alignItems={'center'}
          justifyItems={'center'}
        >
          <Box sx={{height: boxSize, width: boxSize, borderRadius: '25%', backgroundColor: color}} />
          <Typography sx={{fontSize: '0.68rem', fontWeight: 500, color: 'body.500', maxWidth: 300}}>
            {tooltip ?? name}
          </Typography>
        </Box>
        <Typography sx={{fontSize: '0.875rem', fontWeight: 700, color: 'body.800'}}>
          {defaultFormatNumber(value)}
        </Typography>
      </Box>
    );
  }
  return <></>;
};

/**
 * Custom tick component for a bar chart axis, rendering multi-word labels with word wrapping and rotation.
 *
 * @param x - The x-coordinate for the tick label.
 * @param y - The y-coordinate for the tick label.
 * @param payload - The payload object containing the tick value.
 * @param angle - The rotation angle for the tick label.
 * @returns A SVG text element with each word of the label rendered on a separate line, styled according to the theme.
 */
const CustomBarChartTick: React.FC<AnyObject> = ({x, y, payload, angle}) => {
  const words = payload.value.split(' ') as string[];
  const theme = useTheme();
  const yoffset = y + Integers.Twenty;
  return (
    <text
      x={0}
      y={yoffset}
      textAnchor="middle"
      transform={`rotate(${angle}, ${x}, ${yoffset})`}
      fill="#555"
      style={{
        fontSize: '0.6875rem',
        textWrap: 'wrap',
        wordBreak: 'break-word',
        fontWeight: 600,
        color: theme.palette.body[Integers.FiveHundred],
      }}
    >
      {words.map((word, i) => (
        <tspan key={i + 'word'} x={x} dy={i === 0 ? 0 : i * 14}>
          {word}
        </tspan>
      ))}
    </text>
  );
};

/**
 * CustomBarChart is a reusable bar chart component built with Recharts and Material-UI theming.
 *
 * @component
 * @param {IBarChart & CategoricalChartProps} props - The props for the bar chart, including data and chart configuration.
 * @param {Array<{ name: string; value: number; color: string }>} props.data - The data to be displayed in the bar chart.
 * @param {(event: any) => void} [props.onClick] - Optional click handler for bar elements.
 * @param {...CategoricalChartProps} rest - Additional props passed to the underlying BarChart component.
 *
 * @returns {JSX.Element} A responsive bar chart with custom ticks, tooltips, and theming.
 *
 * @example
 * ```tsx
 * <CustomBarChart
 *   data={[
 *     { name: 'A', value: 30, color: '#8884d8' },
 *     { name: 'B', value: 20, color: '#82ca9d' }
 *   ]}
 *   height={300}
 *   onClick={handleBarClick}
 * />
 * ```
 */
const CustomBarChart: React.FC<IBarChart & CategoricalChartProps> = ({data, onClick, ...rest}) => {
  const [angle, setAngle] = React.useState(0);
  const theme = useTheme();

  const renderCustomBarChartTick = useCallback(
    (props: AnyObject) => <CustomBarChartTick {...props} angle={angle} />,
    [angle],
  );
  const lineColor = theme.palette.body[Integers.TwoHundred];
  const commonMargin = Integers.Twenty;
  const angledMargin = angle ? Integers.TwentyEight : Integers.Fifteen;

  const noDataView = () => {
    return (
      <Box
        sx={{
          height: rest.height ?? Integers.ThreeHundred,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography sx={{fontSize: '0.75rem', fontWeight: 500, color: 'body.500'}}>No record to display</Typography>
      </Box>
    );
  };

  const hasData = data && data.length > 0;

  return (
    <ResponsiveContainer
      onResize={width => {
        width && setAngle(width < Integers.FourHundredFifty ? -Integers.ThirtyFive : 0);
        if (width && width < Integers.ThreeHundredFifty) {
          setAngle(-40);
        }
      }}
      width={'100%'}
      height={rest.height ?? Integers.ThreeHundred}
      style={{marginBottom: '1rem'}}
    >
      {!hasData ? (
        noDataView()
      ) : (
        <BarChart
          data={data}
          margin={{top: commonMargin, right: commonMargin, left: -commonMargin, bottom: angledMargin}}
          {...rest}
        >
          <XAxis
            dataKey="name"
            interval={0}
            tick={renderCustomBarChartTick}
            tickLine={{strokeWidth: 0}}
            stroke={lineColor}
          />
          <YAxis
            dataKey="value"
            tickLine={{strokeWidth: 0}}
            tick={{
              fontSize: '0.6875rem',
              fontWeight: 600,
              fill: theme.palette.body[Integers.FiveHundred],
            }}
            stroke={lineColor}
          />
          <Tooltip content={<CustomTooltip />} cursor={false} />

          <Bar
            dataKey="value"
            barSize={62}
            cursor={onClick ? 'pointer' : 'default'}
            onClick={onClick}
            activeBar={false}
          >
            {data.map((entry, index) => (
              <Cell key={'cell-' + index} fill={entry.color} />
            ))}
          </Bar>
        </BarChart>
      )}
    </ResponsiveContainer>
  );
};

export default CustomBarChart;

CustomBarChart.displayName = 'CustomBarChart';
