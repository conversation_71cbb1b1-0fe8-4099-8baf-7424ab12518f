import {Typography, useTheme} from '@mui/material';
import Box from '@mui/material/Box';
import BackdropLoader from 'Components/BackdropLoader';
import {usePermissions} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import {getIndicatorColor} from 'Components/StatusChip/statusChip.util';
import PermissionKey from 'Constants/enums/permissions';
import {AnyObject, getColorFromString, getNameComponents} from 'Helpers/utils';
import {RouteNames} from 'Routes/routeNames';
import {capitalize, isArray} from 'lodash';
import {useCallback, useMemo} from 'react';
import {useNavigate} from 'react-router';
import {useGetTenantStatusMetricsQuery} from 'redux/app/tenantManagementApiSlice';
import {dashboardTenantStatuses, TenantStatus} from 'redux/app/types/tenant.type';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import * as billingUtil from './BillingInvoicePage/billings.utils';
import ChartContainer from './Dashboard/ChartContainer';
import CustomBarChart, {IBarChartData} from './Dashboard/CustomBarChart/CustomBarChart';
import DashboardTable from './Dashboard/DashboardTable';
import TenantOverview from './Dashboard/TenantOverview';
import {getTitleForTenantId} from './Tenants/TenantUtils';
import {tenantStatusMapToStatusChip} from './Tenants/tenants.utils';

type StatusMetricsType = Record<string, {count: number; status: string}>;
type PaymentStatusMetricsType = Record<string, number>;

const Home = () => {
  const {
    data,
    isLoading: loadingData,
    error: statusMetricsError,
  } = useGetTenantStatusMetricsQuery({
    limit: 6,
    order: 'createdOn DESC',
  });
  const {status: statusMetrics, tenants, billingStatusMetrics} = data ?? {};
  const theme = useTheme();

  const provisioningStatusesMetrics = useMemo(() => {
    let arr: IBarChartData[] = [];

    let other: IBarChartData[] = [];
    Object.keys((statusMetrics ?? {}) as StatusMetricsType).forEach(key => {
      const status = tenantStatusMapToStatusChip[Number(key) as TenantStatus];
      const obj: {count: number; status: string} = statusMetrics
        ? (statusMetrics as StatusMetricsType)[key]
        : {count: 0, status: ''};
      const pushObj = {
        name: getTitleForTenantId(key) ?? '',
        value: obj?.count ?? 0,
        color: getColorFromString(theme, getIndicatorColor(status)),
        tag: {
          key: key,
        },
      };
      if (dashboardTenantStatuses.has(Number(key) as TenantStatus)) {
        arr.push(pushObj);
      } else {
        other.push(pushObj);
      }
    });

    if (other.length) {
      const otherCount = other.reduce((acc, curr) => acc + curr.value, 0);
      const name = other.map(o => o.name + ' (' + o.value + ')').join(', ');
      const keys = other
        .map(o => o.tag?.key)
        .filter(Boolean)
        .join(',');
      arr.push({
        name: 'Other',
        value: otherCount,
        color: getColorFromString(theme, 'body.300'),
        tooltip: name,
        tag: {
          key: keys,
        },
      });
    }

    return arr;
  }, [JSON.stringify(statusMetrics ?? {}), theme]);

  const paymentStatusesMetrics = useMemo(
    () =>
      Object.keys((billingStatusMetrics ?? {}) as PaymentStatusMetricsType).reduce<IBarChartData[]>((acc, key) => {
        const count = billingStatusMetrics![key as billingUtil.InvoiceStatus];
        const namedColor = billingUtil.getIndicatorColor(key as billingUtil.InvoiceStatus);
        const pushObj = {
          name: billingUtil.getStatusLabel(key as billingUtil.InvoiceStatus) ?? '',
          value: count ?? 0,
          color: getColorFromString(theme, namedColor),
          tag: {
            key: key,
          },
        };
        acc.push(pushObj);
        return acc;
      }, []) || [],
    [JSON.stringify(billingStatusMetrics ?? {}), theme],
  );

  const {data: currentUser, isLoading: isLoadingCurrentUser} = useGetUserQuery();
  const navigate = useNavigate();
  const {hasPermission} = usePermissions();
  const hasViewBillingPermission = hasPermission(PermissionKey.ViewTenantBillings);
  const hasViewTenantPermission = hasPermission(PermissionKey.ViewTenant);

  const handleProvisioningStatusClick = useCallback(
    (data: AnyObject) => {
      const status = (() => {
        if (data.tag.key?.includes(',')) {
          return data.tag.key.split(',').map((k: string) => k.trim());
        }
        return data?.tag?.key;
      })();
      if (!status && !hasViewTenantPermission) return;
      const statuses = isArray(status) ? new Set(status) : new Set([status]);
      navigate(RouteNames.TENANTS, {
        state: {statusFilter: statuses},
      });
    },
    [navigate],
  );

  const handleBillingStatusClick = useCallback(
    (data: AnyObject) => {
      const status = data?.tag?.key;
      if (!status && !hasViewBillingPermission) return;
      navigate(RouteNames.BILLING_INVOICES, {
        state: {statusFilter: new Set([status])},
      });
    },
    [navigate],
  );

  const drawChart = () => {
    return (
      <Box display={'flex'} width={'100%'} gap={2} flexDirection={{xs: 'column', sm: 'row'}}>
        <ChartContainer title="Provisioning Status Overview" sx={{width: {xs: '100%', sm: '50%'}}}>
          <CustomBarChart
            data={provisioningStatusesMetrics}
            onClick={hasViewTenantPermission ? handleProvisioningStatusClick : undefined}
          />
        </ChartContainer>
        <ChartContainer title="Payment Status Overview" sx={{width: {xs: '100%', sm: '50%'}}}>
          <CustomBarChart
            data={paymentStatusesMetrics}
            onClick={hasViewBillingPermission ? handleBillingStatusClick : undefined}
          />
        </ChartContainer>
      </Box>
    );
  };

  const buildHi = () => {
    const name = capitalize(getNameComponents(currentUser?.firstName)?.first);
    return (
      <Box width={'100%'}>
        <Typography component={'span'} sx={{fontSize: '1.25rem', fontWeight: 700, color: 'body.dark'}}>
          Hi {isLoadingCurrentUser ? '...' : name + '!👋'}
        </Typography>
      </Box>
    );
  };

  if (loadingData) {
    return <BackdropLoader />;
  }

  if (statusMetricsError) {
    return <Typography>Something went wrong</Typography>;
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        gap: 3,
      }}
      data-testid="HomePage"
    >
      {buildHi()}
      <TenantOverview statusMetrics={statusMetrics} error={statusMetricsError} />

      {drawChart()}
      <Box
        sx={{
          display: 'flex',
          flexDirection: {xs: 'column', sm: 'row'},
          alignItems: 'flex-start',
          gap: 1,
        }}
      >
        <DashboardTable tenants={tenants} />
      </Box>
    </Box>
  );
};

export default Home;
