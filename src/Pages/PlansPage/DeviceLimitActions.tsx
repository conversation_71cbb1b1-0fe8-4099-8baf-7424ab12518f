import {Box, BoxProps} from '@mui/material';
import BlueButton from 'Components/BlueButton/BlueButton';
import BorderButton from 'Components/BorderButton/BorderButton';
import {useFormikContext} from 'formik';
import {DeviceConfigForm} from './plans.utils';

export interface DeviceLimitActionsProps extends BoxProps {
  mode: 'view' | 'edit';
  onCancel: () => void;
}

/**
 * Renders action buttons for device limit form, including "Cancel" and "Save Changes".
 *
 * - "Cancel" button sets the mode to 'view'.
 * - "Save Changes" button submits the form, enabled only when the form is valid, dirty, not submitting, and not validating.
 * - Button labels and loading states are managed based on form submission status.
 *
 * @param mode - Current mode of the parent component (e.g., 'edit', 'view').
 * @param setMode - Function to update the mode.
 * @param rest - Additional props passed to the container Box.
 *
 * @returns JSX.Element containing the action buttons.
 */
const DeviceLimitActions: React.FC<DeviceLimitActionsProps> = ({mode, onCancel, ...rest}) => {
  const {handleSubmit, isSubmitting, isValid, dirty, isValidating} = useFormikContext<{
    items: DeviceConfigForm[];
  }>();

  // Enable submit only when form is valid, has changes, not submitting, and not validating
  const canSubmit = !isSubmitting && isValid && dirty && !isValidating;

  const getSubmitTitle = () => {
    if (isSubmitting) {
      return 'Saving...';
    }
    return 'Save Changes';
  };

  return (
    <Box {...rest} display="flex" justifyContent="flex-end" gap={2}>
      <BorderButton
        data-testid="close-button"
        onClick={onCancel}
        sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
      >
        Cancel
      </BorderButton>
      <BlueButton
        data-testid="submit-button"
        loadingPosition="end"
        loading={isSubmitting}
        disabled={!canSubmit}
        sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
        onClick={() => handleSubmit()}
      >
        {getSubmitTitle()}
      </BlueButton>
    </Box>
  );
};

export default DeviceLimitActions;
