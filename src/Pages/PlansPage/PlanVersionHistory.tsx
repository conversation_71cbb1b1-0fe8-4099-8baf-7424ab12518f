import {KeyboardArrowDown, KeyboardArrowUp} from '@mui/icons-material';
import {Box, Grid, Typography} from '@mui/material';
import {Integers} from 'Helpers/integers';
import {useState} from 'react';
import {PlanResponse} from 'redux/app/types/plan.type';
import PlanHistory from './PlanHistory';

interface PlanVersionHistoryProps {
  injectedValues?: PlanResponse;
  minExpandedHeight?: string | number;
}

const paddingX = 2; // 16px

/**
 * Renders a collapsible section displaying the version history of a plan.
 *
 * @param injectedValues - An object containing the plan history data and related values to be injected into the component.
 * @param minExpandedHeight - Optional minimum height for the expanded version history section (defaults to '50vh' if not provided).
 *
 * The component displays a header ("Plan version history") that toggles the visibility of the version history list.
 * When expanded, it renders the `PlanHistory` component with the provided `injectedValues`.
 * The section is styled with a border, background color, and vertical stacking.
 */
const PlanVersionHistory: React.FC<PlanVersionHistoryProps> = ({injectedValues, minExpandedHeight}) => {
  const [open, setOpen] = useState(false);

  return (
    <Box
      sx={{
        width: '100%',
        transform: 'rotate(0deg)',
        opacity: 1,
        gap: 2,
        paddingY: 2,
        borderRadius: '6px',
        borderWidth: '1px',
        borderStyle: 'solid',
        borderColor: 'body.100',
        backgroundColor: 'table.accordianBg',
        display: 'flex',
        flexDirection: 'column', // stack vertically
        alignItems: 'stretch', // make both child grids full width
        marginTop: '1.5rem',
      }}
    >
      <Box display="flex" justifyContent="space-between" flexDirection={'column'}>
        <Box
          display={'flex'}
          flexDirection={'row'}
          sx={{cursor: 'pointer', paddingX: paddingX}}
          alignItems={'center'}
          justifyContent={'space-between'}
          onClick={() => setOpen(prev => !prev)}
        >
          <Typography
            sx={{
              fontWeight: 700, // Bold weight
              fontSize: '1rem',
              lineHeight: '20px',
              letterSpacing: '1%',
              verticalAlign: 'middle',
              color: 'body.dark',
            }}
          >
            Plan version history
          </Typography>
          {open ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
        </Box>
        {open && (
          <Grid
            container
            size={{xs: 12}}
            sx={{
              maxHeight: minExpandedHeight ?? '50vh',
              overflowY: 'auto',
              mt: 1.5,
            }}
          >
            <PlanHistory injectedValues={injectedValues} contentPaddingX={paddingX} />
            {(injectedValues?.planHistories?.length ?? 0) > Integers.One && <Box sx={{height: '0.5rem'}} />}
            {/* Spacer at the bottom */}
          </Grid>
        )}
      </Box>
    </Box>
  );
};

export default PlanVersionHistory;

PlanVersionHistory.displayName = 'PlanVersionHistory';
