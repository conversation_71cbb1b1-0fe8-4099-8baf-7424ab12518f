// Additional imports for direct PlanFormFields test
// @vitest-environment jsdom
import '@testing-library/jest-dom';
import {act, fireEvent, render, screen, waitFor} from '@testing-library/react';
import * as formik from 'formik';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {vi} from 'vitest';
import AddPlan from './AddPlan';
import PlanFormFields from './PlanFormFields';
import * as plansUtils from './plans.utils';

// Mock RTK Query hooks
const mockCreatePlan = vi.fn();
const mockGetDevicesQuery = vi.fn();
const mockGetTenuresQuery = vi.fn();
const mockGetCurrenciesQuery = vi.fn();

vi.mock('redux/app/planManagementAPiSlice', () => ({
  useCreatePlanMutation: () => [mockCreatePlan, {isLoading: false}],
  useGetDevicesQuery: () => mockGetDevicesQuery(),
  useGetTenuresQuery: () => mockGetTenuresQuery(),
  useGetCurrenciesQuery: () => mockGetCurrenciesQuery(),
}));

// Mock notistack
const mockEnqueueSnackbar = vi.fn();
vi.mock('notistack', () => ({
  useSnackbar: () => ({
    enqueueSnackbar: mockEnqueueSnackbar,
  }),
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}));

// Mock child components
vi.mock('Components/BackdropLoader/BackdropLoader', () => ({
  __esModule: true,
  default: () => <div data-testid="BackdropLoader" />,
}));
vi.mock('Components/Breadcrumb/Breadcrumb', () => ({
  __esModule: true,
  default: () => <div data-testid="Breadcrumb" />,
}));
vi.mock('Components/Forms/Form', () => ({
  __esModule: true,
  default: ({children, onSubmit}: {children: React.ReactNode; onSubmit: (values: any) => void}) => (
    <form data-testid="Form" onSubmit={onSubmit}>
      {children}
    </form>
  ),
}));
vi.mock('Components/Forms/FormInput', () => ({
  __esModule: true,
  default: ({id, ...props}: any) => <input data-testid={`form-input-${id}`} {...props} />,
}));

// Helper to trigger form submit with correct values
const triggerFormSubmit = () => {
  const submitBtn = screen.getByRole('button', {name: /add plan/i});
  expect(submitBtn).not.toBeDisabled();
  fireEvent.click(submitBtn);
};

describe('AddPlan', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('shows loader when loading', () => {
    mockGetDevicesQuery.mockReturnValue({isLoading: true});
    mockGetTenuresQuery.mockReturnValue({isLoading: false});
    mockGetCurrenciesQuery.mockReturnValue({isLoading: false});
    render(<AddPlan />);
    expect(screen.getByTestId('BackdropLoader')).toBeInTheDocument();
  });

  it('shows error when any query errors', () => {
    mockGetDevicesQuery.mockReturnValue({isLoading: false, error: true});
    mockGetTenuresQuery.mockReturnValue({isLoading: false, error: false});
    mockGetCurrenciesQuery.mockReturnValue({isLoading: false, error: false});
    render(<AddPlan />);
    expect(screen.getByTestId('AddPlanError')).toBeInTheDocument();
    expect(screen.getByText(/Failed to fetch required data/i)).toBeInTheDocument();
  });

  it('renders form and PlanFormFields/PlanFormButtons when data is loaded', () => {
    mockGetDevicesQuery.mockReturnValue({isLoading: false, data: [{id: '1', min: 1, max: 10}]});
    mockGetTenuresQuery.mockReturnValue({isLoading: false, data: [{id: '2', cycleName: 'monthly'}]});
    mockGetCurrenciesQuery.mockReturnValue({
      isLoading: false,
      data: [{id: '3', symbol: '$', currencyCode: 'USD', currencyName: 'US Dollar', country: 'US'}],
    });

    const setFieldValue = vi.fn();
    const setFieldTouched = vi.fn();
    vi.spyOn(formik, 'useFormikContext').mockReturnValue({
      values: {
        name: '',
        price: 0,
        configureDeviceId: '',
        tier: '',
        billingCycleId: '',
        costPerUser: undefined,
        allowedUnlimitedUsers: false,
      },
      setFieldValue,
      setFieldTouched,
      handleBlur: vi.fn(),
      isValid: true,
      dirty: true,
      errors: {},
      touched: {},
    } as any);

    renderWithTheme(<AddPlan />);
    expect(screen.getByTestId('AddPlanPage')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-name')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-price')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-costPerUser')).toBeInTheDocument();
    expect(screen.getByTestId('submit-button')).toBeInTheDocument();
    expect(screen.getByTestId('cancel-button')).toBeInTheDocument();
  });

  it('navigates on cancel', () => {
    mockGetDevicesQuery.mockReturnValue({isLoading: false, data: [{id: '1', min: 1, max: 10}]});
    mockGetTenuresQuery.mockReturnValue({isLoading: false, data: [{id: '2', cycleName: 'monthly'}]});
    mockGetCurrenciesQuery.mockReturnValue({
      isLoading: false,
      data: [{id: '3', symbol: '$', currencyCode: 'USD', currencyName: 'US Dollar', country: 'US'}],
    });

    const setFieldValue = vi.fn();
    vi.spyOn(formik, 'useFormikContext').mockReturnValue({
      values: {
        name: '',
        price: 0,
        configureDeviceId: '',
        tier: '',
        billingCycleId: '',
        costPerUser: undefined,
        allowedUnlimitedUsers: false,
      },
      setFieldValue,
      setFieldTouched: vi.fn(),
      handleBlur: vi.fn(),
      isValid: true,
      dirty: true,
      errors: {},
      touched: {},
    } as any);

    renderWithTheme(<AddPlan />);
    fireEvent.click(screen.getByRole('button', {name: /cancel/i}));
    expect(mockNavigate).toHaveBeenCalledWith('/plans');
  });

  it('submits form and shows success snackbar', async () => {
    mockGetDevicesQuery.mockReturnValue({isLoading: false, data: [{id: '1', min: 1, max: 10}]});
    mockGetTenuresQuery.mockReturnValue({isLoading: false, data: [{id: '2', cycleName: 'monthly'}]});
    mockGetCurrenciesQuery.mockReturnValue({
      isLoading: false,
      data: [{id: '3', symbol: '$', currencyCode: 'USD', currencyName: 'US Dollar', country: 'US'}],
    });
    mockCreatePlan.mockReturnValue({unwrap: () => Promise.resolve({})});

    const setFieldValue = vi.fn();
    vi.spyOn(formik, 'useFormikContext').mockReturnValue({
      values: {
        name: 'TestPlan',
        price: 10,
        configureDeviceId: '1',
        tier: 'premium',
        billingCycleId: '2',
        costPerUser: 5,
        allowedUnlimitedUsers: false,
      },
      setFieldValue,
      setFieldTouched: vi.fn(),
      handleBlur: vi.fn(),
      isValid: true,
      dirty: true,
      errors: {},
      touched: {
        name: true,
        price: true,
        configureDeviceId: true,
        tier: true,
        billingCycleId: true,
        costPerUser: true,
        allowedUnlimitedUsers: true,
      },
    } as any);

    renderWithTheme(<AddPlan />);
    await act(async () => {
      fireEvent.submit(screen.getByTestId('Form'));
    });

    await waitFor(() => {
      expect(mockCreatePlan).toHaveBeenCalled();
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith('Plan added successfully!', {variant: 'success'});
      expect(mockNavigate).toHaveBeenCalledWith('/plans');
    });
  });

  it('toggles unlimited users and disables cost per user input', () => {
    mockGetDevicesQuery.mockReturnValue({isLoading: false, data: [{id: '1', min: 1, max: 10}]});
    mockGetTenuresQuery.mockReturnValue({isLoading: false, data: [{id: '2', cycleName: 'monthly'}]});
    mockGetCurrenciesQuery.mockReturnValue({
      isLoading: false,
      data: [{id: '3', symbol: '$', currencyCode: 'USD', currencyName: 'US Dollar', country: 'US'}],
    });

    const setFieldValue = vi.fn();
    vi.spyOn(formik, 'useFormikContext').mockReturnValue({
      values: {
        name: '',
        price: 0,
        configureDeviceId: '',
        tier: '',
        billingCycleId: '',
        costPerUser: undefined,
        allowedUnlimitedUsers: false,
      },
      setFieldValue,
      setFieldTouched: vi.fn(),
      handleBlur: vi.fn(),
      isValid: true,
      dirty: true,
      errors: {},
      touched: {},
    } as any);

    renderWithTheme(<AddPlan />);
    const costPerUserInput = screen.getByTestId('form-input-costPerUser');
    expect(costPerUserInput).not.toBeDisabled();
    // Toggle unlimited users
    const toggle = screen.getByTestId('unlimited-users-toggle');
    fireEvent.click(toggle);
    expect(setFieldValue).toHaveBeenCalledWith('allowedUnlimitedUsers', true);
  });

  it('renders cost per user input as hidden and disabled when allowedUnlimitedUsers is true', () => {
    // Mock form field default value differently if needed using query state
    const setFieldValue = vi.fn();
    vi.spyOn(formik, 'useFormikContext').mockReturnValue({
      values: {
        name: '',
        price: 0,
        configureDeviceId: '',
        tier: '',
        billingCycleId: '',
        costPerUser: undefined,
        allowedUnlimitedUsers: true, // Set to true so input should be disabled
      },
      setFieldValue,
      setFieldTouched: vi.fn(),
      handleBlur: vi.fn(),
      isValid: true,
      dirty: true,
      errors: {},
      touched: {},
    } as any);

    renderWithTheme(<AddPlan />);
    const costInput = screen.getByTestId('form-input-costPerUser');
    expect(costInput).toBeDisabled();
    expect(costInput.parentElement).toHaveStyle('visibility: hidden');
  });

  // Covers PlanFormFields edit mode styles, version history panel, and input sanitization
  describe('PlanFormFields edit mode and input handlers', () => {
    const deviceOptions = [{value: '1', label: 'Device 1'}];
    const tenureOptions = [{value: '2', label: 'Monthly'}];
    const currencyCodeOptions = {
      id: 'usd',
      symbol: '$',
      currencyCode: 'USD',
      currencyName: 'US Dollar',
      country: 'US',
    };

    it('applies edit styles and renders version history panel (collapsed and expanded)', () => {
      const setFieldValue = vi.fn();
      vi.spyOn(formik, 'useFormikContext').mockReturnValue({
        values: {
          name: 'EditPlan',
          price: 100,
          configureDeviceId: '1',
          tier: 'premium',
          billingCycleId: '2',
          costPerUser: 10,
          allowedUnlimitedUsers: false,
        },
        setFieldValue,
      } as any);

      renderWithTheme(
        <PlanFormFields
          isEdit={true}
          deviceOptions={deviceOptions}
          tenureOptions={tenureOptions}
          currencyCodeOptions={currencyCodeOptions}
          injectedValues={{id: 'plan1'} as any}
        />,
      );

      // Version history panel should be collapsed initially
      expect(screen.getByText('Plan version history')).toBeInTheDocument();
      // Expand the panel
      fireEvent.click(screen.getByText('Plan version history'));
      // Should render PlanHistory when expanded (mocked, so just check for injectedValues prop if possible)
      // Optionally check for the up arrow icon
      expect(screen.getByText('Plan version history')).toBeInTheDocument();
    });

    it('sanitizes non-numeric input for price and costPerUser', () => {
      const setFieldValue = vi.fn();
      vi.spyOn(formik, 'useFormikContext').mockReturnValue({
        values: {
          name: '',
          price: 0,
          configureDeviceId: '',
          tier: '',
          billingCycleId: '',
          costPerUser: undefined,
          allowedUnlimitedUsers: false,
        },
        setFieldValue,
      } as any);

      renderWithTheme(
        <PlanFormFields
          isEdit={false}
          deviceOptions={deviceOptions}
          tenureOptions={tenureOptions}
          currencyCodeOptions={currencyCodeOptions}
        />,
      );

      const priceInput = screen.getByTestId('form-input-price') as HTMLInputElement;
      fireEvent.input(priceInput, {target: {value: 'abc123'}});
      expect(priceInput.value).toBe('123');

      const costPerUserInput = screen.getByTestId('form-input-costPerUser') as HTMLInputElement;
      fireEvent.input(costPerUserInput, {target: {value: 'xyz456'}});
      expect(costPerUserInput.value).toBe('456');
    });
  });
});

describe('PlanFormFields renderValue', () => {
  const deviceOptions = [{value: '1', label: 'Device 1'}];
  const tenureOptions = [{value: '2', label: 'Monthly'}];
  const currencyCodeOptions = {
    id: 'usd',
    symbol: '$',
    currencyCode: 'USD',
    currencyName: 'US Dollar',
    country: 'US',
  };

  it('covers renderValue with features', async () => {
    vi.spyOn(formik, 'useFormikContext').mockReturnValue({
      values: {
        name: '',
        price: 0,
        configureDeviceId: '',
        tier: plansUtils.PlanTierType.PREMIUM,
        billingCycleId: '',
        costPerUser: undefined,
        allowedUnlimitedUsers: false,
      },
      setFieldValue: vi.fn(),
    } as any);

    // Patch groupedFeatureOptions to include an option with features
    plansUtils.groupedFeatureOptions.push({
      value: plansUtils.PlanTierType.PREMIUM,
      label: 'Test Premium',
      features: ['Feature 1', 'Feature 2'],
    });

    renderWithTheme(
      <PlanFormFields
        isEdit={false}
        deviceOptions={deviceOptions}
        tenureOptions={tenureOptions}
        currencyCodeOptions={currencyCodeOptions}
      />,
    );
    // Open the select dropdown to make options visible
    const {container} = renderWithTheme(
      <PlanFormFields
        isEdit={false}
        deviceOptions={deviceOptions}
        tenureOptions={tenureOptions}
        currencyCodeOptions={currencyCodeOptions}
      />,
    );
    // Find all selects and pick the one with id="tier"
    const selects = screen.getAllByTestId('select');
    const tierSelect = Array.from(selects).find(el => el.querySelector('#tier'));
    fireEvent.mouseDown(tierSelect!.querySelector('#tier')!);

    // Check for label and features in the dropdown options
    expect(await screen.findByText('Test Premium')).toBeInTheDocument();
    expect(await screen.findByText('Feature 1, Feature 2')).toBeInTheDocument();

    // Restore groupedFeatureOptions
    plansUtils.groupedFeatureOptions.pop();
  });

  it('covers renderValue without features', async () => {
    vi.spyOn(formik, 'useFormikContext').mockReturnValue({
      values: {
        name: '',
        price: 0,
        configureDeviceId: '',
        tier: plansUtils.PlanTierType.STANDARD,
        billingCycleId: '',
        costPerUser: undefined,
        allowedUnlimitedUsers: false,
      },
      setFieldValue: vi.fn(),
    } as any);

    // Patch groupedFeatureOptions to include an option without features
    plansUtils.groupedFeatureOptions.push({
      value: plansUtils.PlanTierType.STANDARD,
      label: 'Test Standard',
      features: [],
    });

    renderWithTheme(
      <PlanFormFields
        isEdit={false}
        deviceOptions={deviceOptions}
        tenureOptions={tenureOptions}
        currencyCodeOptions={currencyCodeOptions}
      />,
    );
    // Open the select dropdown to make options visible
    const {container} = renderWithTheme(
      <PlanFormFields
        isEdit={false}
        deviceOptions={deviceOptions}
        tenureOptions={tenureOptions}
        currencyCodeOptions={currencyCodeOptions}
      />,
    );
    // Find all selects and pick the one with id="tier"
    const selects = screen.getAllByTestId('select');
    const tierSelect = Array.from(selects).find(el => el.querySelector('#tier'));
    fireEvent.mouseDown(tierSelect!.querySelector('#tier')!);

    // Check for label in the dropdown options
    expect(await screen.findByText('Test Standard')).toBeInTheDocument();

    // Restore groupedFeatureOptions
    plansUtils.groupedFeatureOptions.pop();
  });
});
