import StatusChip from 'Components/StatusChip/StatusChip';
import {StatusChipState} from 'Components/StatusChip/statusChip.util';
import {isNil} from 'lodash';
import {PlanStatus} from './plans.utils';

/**
 * Renders a status chip for a given plan status.
 *
 * This component maps the provided `status` (which can be a `PlanStatus` enum value or a string)
 * to the appropriate `StatusChipState` and displays a `StatusChip` accordingly.
 *
 * @param props.status - The status of the plan, either as a `PlanStatus` enum value or a string representation.
 * @returns A React fragment containing the `StatusChip` if the status is recognized, otherwise `null`.
 */
const PlanStatusChip: React.FC<{status: PlanStatus | string}> = ({status}) => {
  const statusChipStatus = {
    [PlanStatus.ACTIVE]: StatusChipState.ACTIVE,
    [PlanStatus.INACTIVE]: StatusChipState.INACTIVE,
    [PlanStatus.TRIAL]: StatusChipState.PENDINGONBOARDING,
  };
  let mappedStatus: PlanStatus | undefined;
  if (typeof status === 'string') {
    if (status.toLowerCase() === String(PlanStatus.ACTIVE).toLowerCase()) {
      mappedStatus = PlanStatus.ACTIVE;
    } else if (status.toLowerCase() === String(PlanStatus.INACTIVE).toLowerCase()) {
      mappedStatus = PlanStatus.INACTIVE;
    } else {
      mappedStatus = undefined; // for sonar
    }
  } else {
    mappedStatus = status;
  }
  return <> {!isNil(mappedStatus) && <StatusChip status={statusChipStatus[mappedStatus]} />} </>;
};

export default PlanStatusChip;

PlanStatusChip.displayName = 'PlanStatusChip';
