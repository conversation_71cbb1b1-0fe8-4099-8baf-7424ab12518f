import {Box, Divider, Typography} from '@mui/material';
import EditIcon from 'Assets/EditIcon';
import BackdropLoader from 'Components/BackdropLoader';
import BorderButton from 'Components/BorderButton/BorderButton';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import Form from 'Components/Forms/Form';
import {FormActions} from 'Components/Forms/Form/FormUtils';
import PermissionWrapper from 'Components/PermissionWrapper';
import PermissionKey from 'Constants/enums/permissions';
import {useSnackbar} from 'notistack';
import React from 'react';
import {useNavigate} from 'react-router-dom';
import {useGetDevicesQuery, useUpdateDevicesMutation} from 'redux/app/planManagementAPiSlice';
import {IConfigureDevice} from 'redux/app/types/plan.type';
import {RouteNames} from 'Routes/routeNames';
import DeviceLimitActions from './DeviceLimitActions';
import EditDeviceLimitsFormikForm from './EditDeviceLimits';
import {DeviceConfigFormikValues, getDeviceConfigFormValidationSchema} from './plans.utils';

/**
 * `DeviceLimits` is a React functional component for managing device limit configurations within the Plans page.
 *
 * Features:
 * - Fetches device configuration data using `useGetDevicesQuery`.
 * - Allows users to view and edit device limits, including updating and deleting device configurations.
 * - Handles form submission, validation, and error/success notifications via snackbars.
 * - Displays breadcrumbs for navigation context.
 * - Provides UI controls for editing, saving, and closing the device limits panel.
 * - Uses Formik for form state management and validation.
 *
 * State:
 * - `mode`: Controls whether the component is in 'view' or 'edit' mode.
 *
 * API:
 * - Submits only edited or deleted device configurations to the backend.
 * - Refetches device data after successful updates.
 *
 * UI:
 * - Shows a loading backdrop while fetching device data.
 * - Renders top section with title and breadcrumbs.
 * - Renders form for editing device limits.
 * - Renders bottom section with a close button in view mode.
 *
 * @returns {JSX.Element} The rendered DeviceLimits component.
 */
const DeviceLimits: React.FC = () => {
  const navigate = useNavigate();
  const [mode, setMode] = React.useState<'view' | 'edit'>('view');
  const {
    data: deviceData,
    isFetching: deviceLoading,
    error: deviceError,
    refetch: refetchDevices,
  } = useGetDevicesQuery({
    order: 'min asc',
    limit: 1000,
    offset: 0,
  });
  const [updateDevices] = useUpdateDevicesMutation();

  const {enqueueSnackbar} = useSnackbar();

  React.useEffect(() => {
    if (deviceError) {
      enqueueSnackbar('Failed to get device data', {variant: 'error'});
    }
  }, [deviceError, enqueueSnackbar]);

  const PX = 1.5;

  const breadcrumbs = () => {
    const defaultBreadCrumb = [
      {
        label: 'Plans',
        url: RouteNames.PLANS,
      },
      {
        label: 'Device Limits',
        url: RouteNames.DEVICE_LIMITS,
      },
    ];

    if (mode === 'edit') {
      return [
        ...defaultBreadCrumb,
        {
          label: 'Edit Device Limits',
          url: RouteNames.DEVICE_LIMITS,
        },
      ];
    }
    return defaultBreadCrumb;
  };

  const onCancel = () => {
    setMode('view');
    refetchDevices();
  };

  /**
   * Handles the submission of device configuration changes.
   *
   * This function processes the form values to identify edited and deleted device items,
   * transforms them into the required format for backend submission, and combines them.
   * If there are changes, it calls the `updateDevices` API and provides user feedback via snackbars.
   * On success, it switches the mode to 'view' and refetches device data.
   *
   * @param _formValues - The current form values containing device items and deleted items.
   * @param _action - Formik form actions (unused).
   */
  const onSubmit = async (_formValues: DeviceConfigFormikValues, _action: FormActions<DeviceConfigFormikValues>) => {
    // Filter only edited rows for submission
    const editedItems = _formValues.items.filter(item => item.isEdited);

    // Get deleted items
    const deletedItems = _formValues.deletedItems || [];

    // Transform edited items
    const transformedEditedData = editedItems.map(item => {
      const tempItem = item as unknown as IConfigureDevice;
      return {
        min: Number(item.min),
        max: Number(item.max),
        id: tempItem.id,
      };
    });

    // Transform deleted items - add deleted: true flag
    const transformedDeletedData = deletedItems.map(item => {
      const tempItem = item as unknown as IConfigureDevice;
      return {
        min: Number(item.min),
        max: Number(item.max),
        id: tempItem.id,
        deleted: true, // Mark as deleted for backend
      };
    });

    // Combine edited and deleted items
    const allChanges = [...transformedEditedData, ...transformedDeletedData];

    // Only submit if there are changes
    if (allChanges.length === 0) {
      setMode('view');
      return;
    }

    try {
      const res = await updateDevices(allChanges as unknown as IConfigureDevice[]);
      if (res?.error || res?.data?.error) {
        enqueueSnackbar(`${res?.data?.error}`, {variant: 'error'});
        return;
      }
      enqueueSnackbar(`Device limits saved successfully!`, {
        variant: 'success',
        subMessage: `${allChanges.length} device limits have been updated.`,
      });
      setMode('view');
      refetchDevices();
    } catch {
      enqueueSnackbar(`Failed to save device limits. Please try again.`, {variant: 'error'});
    }
  };

  /**
   * Returns the initial values for the device configuration form.
   *
   * Transforms the provided `deviceData` from the API to match the form structure,
   * initializing each item with an `isEdited` flag set to `false` and an empty array for `deletedItems`.
   * If no `deviceData` is available, returns a fallback structure with default values.
   *
   * @returns {DeviceConfigFormikValues} The initial form values for device configuration.
   */
  const getInitialValue = (): DeviceConfigFormikValues => {
    // Transform API data to match form structure
    if (deviceData && deviceData.length > 0) {
      return {
        items: deviceData.map((item: IConfigureDevice) => ({
          ...item,
          isEdited: false, // Initialize tracking field
        })),
        deletedItems: [], // Initialize deleted items array
      };
    }

    // Fallback when no data
    return {
      items: [
        {
          min: undefined,
          max: undefined,
          isEdited: false, // Initialize tracking field
        },
      ],
      deletedItems: [], // Initialize deleted items array
    };
  };

  // Recalculate initial values when deviceData changes
  const initialValues = React.useMemo(() => getInitialValue(), [deviceData]);

  if (deviceLoading) {
    return <BackdropLoader />;
  }

  /**
   * Builds the top part of the Device Limits section UI.
   *
   * This component renders a flex container with the section title, breadcrumbs,
   * and an edit button. The edit button triggers the mode change to 'edit' when clicked.
   *
   * @returns {JSX.Element} The rendered top part of the Device Limits section.
   */
  const buildTopPart = () => {
    return (
      <Box display="flex" alignItems="center" gap={1} data-testid="top-part">
        <Box display={'flex'} flexDirection={'column'} gap={1}>
          <Box display={'flex'} flexDirection={'row'} alignItems="center" gap={1}>
            <Typography sx={{fontSize: '1.25rem', fontWeight: 700}}>Device Limits</Typography>
          </Box>
          <Box sx={{display: 'flex', alignItems: 'center'}}>
            <Box
              sx={{
                height: 12,
                borderColor: 'body.100',
              }}
            />
            <Breadcrumb items={breadcrumbs()} separator="|" />
          </Box>
        </Box>
        <PermissionWrapper permission={PermissionKey.UpdateConfigureDevices}>
          <Box sx={{ml: 'auto', display: 'flex', flexDirection: 'row', gap: 1, alignItems: 'center'}}>
            {mode === 'view' && (
              <BorderButton
                data-testid="edit-button"
                sx={{fontWeight: 700, px: '1.56rem', color: 'body.dark'}}
                onClick={() => setMode('edit')}
              >
                <Box sx={{display: 'flex', alignItems: 'center', gap: 0.5}}>
                  <EditIcon
                    sx={{
                      fontSize: '1.25rem',
                      fill: theme => theme.palette.white.main,
                      color: 'body.500',
                    }}
                    data-testid="edit-icon"
                  />
                  Edit
                </Box>
              </BorderButton>
            )}
          </Box>
        </PermissionWrapper>
      </Box>
    );
  };

  const buildBottomSection = () => {
    return (
      <Box sx={{display: 'flex', flexDirection: 'column', mt: 'auto', mb: 1.5}} data-testid="bottom-section">
        <Divider sx={{my: 2}} />
        <Box sx={{alignSelf: 'flex-end'}} px={PX}>
          {mode === 'view' ? (
            <BorderButton
              sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
              fullWidth={false}
              onClick={() => navigate(-1)}
            >
              Go To Plans
            </BorderButton>
          ) : (
            <DeviceLimitActions mode={mode} onCancel={onCancel} />
          )}
        </Box>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 0.5,
      }}
    >
      {buildTopPart()}

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1.5,
        }}
      >
        <Box
          sx={{
            border: '1px solid',
            borderColor: 'body.200',
            borderRadius: '0.375rem',
            pt: 1,
            minHeight: '80vh',
            display: 'flex',
            flexDirection: 'column',
          }}
          data-testid="lead-detail"
        >
          <Form
            initialValues={initialValues}
            enableReinitialize={true}
            validateOnChange={true}
            validateOnBlur={true}
            validationSchema={getDeviceConfigFormValidationSchema()}
            onSubmit={(v, a) => onSubmit(v, a)}
          >
            <Box sx={{display: 'flex', flexDirection: 'column', flex: 1, minHeight: '80vh'}}>
              <EditDeviceLimitsFormikForm mode={mode} sx={{px: 1.5, py: 1}} />
            </Box>
            {buildBottomSection()}
          </Form>
        </Box>
      </Box>
    </Box>
  );
};

export default DeviceLimits;
