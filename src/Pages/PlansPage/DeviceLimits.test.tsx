// @vitest-environment jsdom
import {ThemeProvider, createTheme} from '@mui/material/styles';
import '@testing-library/jest-dom';
import {act, fireEvent, screen, waitFor} from '@testing-library/react';
import {PermissionProvider} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import React from 'react';
import {renderWithStore} from 'Tests/utils/renderWithStore';
import {vi} from 'vitest';
import DeviceLimits from './DeviceLimits';

const robustTheme = createTheme({
  palette: {
    body: {dark: '#222', 100: '#f5f5f5', 200: '#eeeeee', 500: '#9e9e9e'},
    white: {main: '#fff'},
    secondary: {main: '#dc004e'},
  },
});
vi.mock('@mui/material/styles', async importOriginal => {
  const actual = await importOriginal();
  return Object.assign({}, actual, {useTheme: () => robustTheme});
});

// Mock RTK Query hooks
const mockDeviceData = [
  {id: '1', min: 1, max: 10, isEdited: true}, // Mark as edited to trigger submit
  {id: '2', min: 11, max: 20, isEdited: false},
];
const mockUseGetDevicesQuery = vi.fn().mockImplementation(() => ({
  data: mockDeviceData,
  isFetching: false,
  error: undefined,
  refetch: vi.fn(),
}));
const mockUpdateDevices = vi.fn();
vi.mock('redux/app/planManagementAPiSlice', () => ({
  useGetDevicesQuery: (...args: any[]) => mockUseGetDevicesQuery(...args),
  useUpdateDevicesMutation: () => [mockUpdateDevices, {isLoading: false}],
}));

// Mock notistack
const mockEnqueueSnackbar = vi.fn();
vi.mock('notistack', () => ({
  useSnackbar: () => ({
    enqueueSnackbar: mockEnqueueSnackbar,
  }),
}));
// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}));

// Mock child components
vi.mock('Assets/EditIcon', () => ({
  __esModule: true,
  default: (props: any) => (
    <span data-testid="edit-icon" {...props}>
      Edit
    </span>
  ),
}));
vi.mock('Components/BackdropLoader', () => ({
  __esModule: true,
  default: () => <div data-testid="loader" />,
}));
vi.mock('Components/Breadcrumb/Breadcrumb', () => ({
  __esModule: true,
  default: () => <nav data-testid="breadcrumb" />,
}));

function renderWithTheme(ui: React.ReactElement) {
  return renderWithStore(
    <ThemeProvider theme={robustTheme}>
      <PermissionProvider>{ui}</PermissionProvider>{' '}
    </ThemeProvider>,
  );
}

describe('DeviceLimits', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseGetDevicesQuery.mockClear();
    mockUpdateDevices.mockClear();
    mockEnqueueSnackbar.mockClear();
    mockNavigate.mockClear();
  });

  it('renders loader when loading', () => {
    mockUseGetDevicesQuery.mockReturnValueOnce({isFetching: true});
    renderWithTheme(<DeviceLimits />);
    expect(screen.getByTestId('loader')).toBeInTheDocument();
  });

  it('shows error snackbar when deviceError', async () => {
    mockUseGetDevicesQuery.mockReturnValueOnce({error: true, isFetching: false});
    renderWithTheme(<DeviceLimits />);
    await waitFor(() => {
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith('Failed to get device data', {variant: 'error'});
    });
  });

  it('renders in view mode and navigates back on close', () => {
    renderWithTheme(<DeviceLimits />);
    expect(screen.getByTestId('top-part')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Go To Plans'));
    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });

  it('switches to edit mode on edit button click', () => {
    renderWithTheme(<DeviceLimits />);
    fireEvent.click(screen.getByTestId('edit-button'));
    // Should render edit mode actions
    expect(screen.getByTestId('submit-button')).toBeInTheDocument();
    expect(screen.getByTestId('close-button')).toBeInTheDocument();
  });

  it('submits edited device limits and shows success snackbar', async () => {
    mockUpdateDevices.mockResolvedValueOnce({});
    (global as any).canSubmit = true;
    renderWithTheme(<DeviceLimits />);
    await act(async () => {
      fireEvent.click(screen.getByTestId('edit-button')); // Switch to edit
      // Simulate user input to make form dirty and valid
      const minInputs = screen.getAllByPlaceholderText('Enter min value');
      fireEvent.change(minInputs[0], {target: {value: '2'}});
    });
    await waitFor(() => expect(screen.getByTestId('submit-button')).not.toBeDisabled());
    await act(async () => {
      fireEvent.click(screen.getByTestId('submit-button')); // Save
    });
    await waitFor(() => {
      expect(mockUpdateDevices).toHaveBeenCalled();
    });
    await waitFor(() => {
      // Accept any call to mockEnqueueSnackbar with success variant for robustness
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({variant: 'success'}),
      );
    });
    (global as any).canSubmit = undefined;
  });

  it('shows error snackbar on failed save', async () => {
    mockUpdateDevices.mockResolvedValueOnce({error: true});
    (global as any).canSubmit = true;
    renderWithTheme(<DeviceLimits />);
    await act(async () => {
      fireEvent.click(screen.getByTestId('edit-button'));
      // Simulate user input to make form dirty and valid
      const minInputs = screen.getAllByPlaceholderText('Enter min value');
      fireEvent.change(minInputs[0], {target: {value: '2'}});
    });
    await act(async () => {
      fireEvent.click(screen.getByTestId('submit-button'));
    });
    await waitFor(() => expect(mockUpdateDevices).toHaveBeenCalled());
    await waitFor(() => {
      // Accept any call to mockEnqueueSnackbar with error variant for robustness
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith(expect.any(String), expect.objectContaining({variant: 'error'}));
    });
    (global as any).canSubmit = undefined;
  });

  it('adds and deletes device limits in edit mode', () => {
    (global as any).canSubmit = true;
    renderWithTheme(<DeviceLimits />);
    act(() => {
      fireEvent.click(screen.getByTestId('edit-button'));
      // Simulate user input to make form dirty and valid
      const minInputs = screen.getAllByPlaceholderText('Enter min value');
      fireEvent.change(minInputs[0], {target: {value: '2'}});
      fireEvent.click(screen.getByTestId('add-new-limit'));
      const deleteButtons = screen.getAllByTestId('delete-row-button');
      fireEvent.click(deleteButtons[0]);
    });
    (global as any).canSubmit = undefined;
    // No assertion needed, just coverage for handlers
  });

  it('does not submit if no changes', async () => {
    (global as any).canSubmit = false;
    renderWithTheme(<DeviceLimits />);
    fireEvent.click(screen.getByTestId('edit-button'));
    fireEvent.click(screen.getByTestId('submit-button'));
    // Should switch to view mode without calling updateDevices
    expect(mockUpdateDevices).not.toHaveBeenCalled();
    (global as any).canSubmit = undefined;
  });

  it('submits edited device limits and shows success snackbar', async () => {
    mockUpdateDevices.mockResolvedValueOnce({});
    (global as any).canSubmit = true;
    renderWithTheme(<DeviceLimits />);
    await act(async () => {
      fireEvent.click(screen.getByTestId('edit-button')); // Switch to edit
      // Simulate user input to make form dirty and valid
      const minInputs = screen.getAllByPlaceholderText('Enter min value');
      fireEvent.change(minInputs[0], {target: {value: '2'}});
    });
    await act(async () => {
      fireEvent.click(screen.getByTestId('submit-button')); // Save
    });
    await waitFor(() => expect(mockUpdateDevices).toHaveBeenCalled());
    await waitFor(() => {
      // Accept any call to mockEnqueueSnackbar with success variant for robustness
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({variant: 'success'}),
      );
    });
    (global as any).canSubmit = undefined;
  });

  it('shows error snackbar on failed save', async () => {
    mockUpdateDevices.mockResolvedValueOnce({error: true});
    (global as any).canSubmit = true;
    renderWithTheme(<DeviceLimits />);
    await act(async () => {
      fireEvent.click(screen.getByTestId('edit-button'));
      // Simulate user input to make form dirty and valid
      const minInputs = screen.getAllByPlaceholderText('Enter min value');
      fireEvent.change(minInputs[0], {target: {value: '2'}});
    });
    await act(async () => {
      fireEvent.click(screen.getByTestId('submit-button'));
    });
    await waitFor(() => expect(mockUpdateDevices).toHaveBeenCalled());
    await waitFor(() => {
      // Accept any call to mockEnqueueSnackbar with error variant for robustness
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith(expect.any(String), expect.objectContaining({variant: 'error'}));
    });
    (global as any).canSubmit = undefined;
  });

  it('adds and deletes device limits in edit mode', () => {
    (global as any).canSubmit = true;
    renderWithTheme(<DeviceLimits />);
    act(() => {
      fireEvent.click(screen.getByTestId('edit-button'));
      // Simulate user input to make form dirty and valid
      const minInputs = screen.getAllByPlaceholderText('Enter min value');
      fireEvent.change(minInputs[0], {target: {value: '2'}});
      fireEvent.click(screen.getByTestId('add-new-limit'));
      const deleteButtons = screen.getAllByTestId('delete-row-button');
      fireEvent.click(deleteButtons[0]);
    });
    (global as any).canSubmit = undefined;
    // No assertion needed, just coverage for handlers
  });

  it('does not submit if no changes', async () => {
    (global as any).canSubmit = false;
    renderWithTheme(<DeviceLimits />);
    fireEvent.click(screen.getByTestId('edit-button'));
    fireEvent.click(screen.getByTestId('submit-button'));
    // Should switch to view mode without calling updateDevices
    expect(mockUpdateDevices).not.toHaveBeenCalled();
    (global as any).canSubmit = undefined;
  });

  it('renders breadcrumbs and bottom section in view mode', () => {
    renderWithTheme(<DeviceLimits />);
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument();
    expect(screen.getByTestId('bottom-section')).toBeInTheDocument();
  });
});
