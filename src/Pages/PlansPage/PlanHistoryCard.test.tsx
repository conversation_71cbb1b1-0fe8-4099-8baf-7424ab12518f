import {skipToken} from '@reduxjs/toolkit/query';
import '@testing-library/jest-dom'; // extends expect
import {render, screen} from '@testing-library/react';
import {useGetUserByIdQuery} from 'redux/app/tenantManagementApiSlice';
import {afterEach, describe, expect, it, vi} from 'vitest';
import {PlanHistoryCard} from './PlanHistoryCard';
import {PlanStatus} from './plans.utils';
// Mock the RTK query hook module using vitest
vi.mock('redux/app/tenantManagementApiSlice', () => {
  return {
    useGetUserByIdQuery: vi.fn(),
  };
});

vi.mock('Components/PermissionRedirectWrapper/PermissionProvider', () => ({
  usePermissions: vi.fn().mockReturnValue({hasPermission: () => true}),

  PermissionProvider: ({children}: {children: React.ReactNode}) => <>{children}</>,
}));

const mockedUseGetUserByIdQuery = useGetUserByIdQuery as unknown as ReturnType<typeof vi.fn> & {
  mockReturnValue: (v: any) => void;
};

afterEach(() => {
  vi.clearAllMocks();
});

describe('PlanHistoryCard (Vitest + RTL)', () => {
  const baseData = {
    name: 'My Plan',
    numberOfDevices: '42',
    infraConfigurations: 'cloud',
    tenure: 'ANNUAL',
    price: '199',
    costPerUser: '11.9',
    allowedUnlimited: false,
    version: 'v1.2',
    createdAt: '2025-07-02T14:30:00.000Z',
    createdBy: 'system',
    status: PlanStatus.ACTIVE + '',
  };

  it('calls useGetUserByIdQuery with skipToken when updatedBy is not provided', () => {
    (useGetUserByIdQuery as unknown as import('vitest').Mock).mockReturnValue({data: undefined} as any);

    render(<PlanHistoryCard data={baseData as any} />);

    expect(useGetUserByIdQuery).toHaveBeenCalled();
    expect((useGetUserByIdQuery as unknown as import('vitest').Mock).mock.calls[0][0]).toBe(skipToken);
  });

  it('renders version uppercase, date/time and plan name', () => {
    (useGetUserByIdQuery as unknown as import('vitest').Mock).mockReturnValue({data: undefined} as any);

    render(<PlanHistoryCard data={baseData as any} />);

    // Version uppercase
    expect(screen.getByText(baseData.version.toUpperCase())).toBeInTheDocument();

    // Plan name
    expect(screen.getByText(baseData.name)).toBeInTheDocument();

    // Date/time loose checks (avoid timezone flakiness)
    // Expect the combined version text node to include the bullet separators and a short date pattern
    expect(
      screen.getByText(content => {
        return content.includes('•') && /\d{1,2}\s[A-Za-z]{3},\s\d{4}/.test(content);
      }),
    ).toBeInTheDocument();

    // time includes lowercase am/pm
    expect(screen.getByText(content => /\b(am|pm)\b/.test(content))).toBeInTheDocument();
  });

  it('shows Active chip when index is 0', () => {
    (useGetUserByIdQuery as unknown as import('vitest').Mock).mockReturnValue({data: undefined} as any);

    render(<PlanHistoryCard data={baseData as any} index={0} />);

    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  it('formats price, users and capitalizes infra and tenure', () => {
    (useGetUserByIdQuery as unknown as import('vitest').Mock).mockReturnValue({data: undefined} as any);

    render(<PlanHistoryCard data={baseData as any} />);

    // Price formatted to two decimals
    expect(screen.getByText('$199.00')).toBeInTheDocument();

    // Users formatted as $11.90 per user
    expect(screen.getByText('$11.90 per user')).toBeInTheDocument();

    // Infra capitalized to 'Cloud'
    expect(screen.getByText('Cloud')).toBeInTheDocument();

    // Tenure formatted to 'Annual'
    expect(screen.getByText('Annual')).toBeInTheDocument();
  });

  it('shows "Unlimited users" when allowedUnlimited is true', () => {
    const unlimitedData = {...baseData, allowedUnlimited: true, costPerUser: '0'};
    (useGetUserByIdQuery as unknown as import('vitest').Mock).mockReturnValue({data: undefined} as any);

    render(<PlanHistoryCard data={unlimitedData as any} />);

    expect(screen.getByText('Unlimited users')).toBeInTheDocument();
  });

  it('renders Avatar initials and Updated by text when userData exists', () => {
    const user = {
      firstName: 'John',
      lastName: 'Doe',
      roleName: 'Admin',
    };
    (useGetUserByIdQuery as unknown as import('vitest').Mock).mockReturnValue({data: [user]} as any);

    render(<PlanHistoryCard data={baseData as any} updatedBy="user-123" />);

    // Avatar initials JD
    expect(screen.getByText('JD')).toBeInTheDocument();

    // Updated by full name + role (component uses " • " separator)
    expect(screen.getByText(/John Doe\s*•\s*Admin/)).toBeInTheDocument();

    // Hook called with provided updatedBy id
    expect((useGetUserByIdQuery as unknown as import('vitest').Mock).mock.calls[0][0]).toBe('user-123');
  });

  it('renders empty avatar and no updated-by text when userData empty', () => {
    (useGetUserByIdQuery as unknown as import('vitest').Mock).mockReturnValue({data: []} as any);

    render(<PlanHistoryCard data={baseData as any} updatedBy="user-456" />);

    // No initials (JD) shown
    expect(screen.queryByText('JD')).not.toBeInTheDocument();

    // No updated-by role/text shown
    expect(screen.queryByText(/Admin/)).not.toBeInTheDocument();
  });
});
