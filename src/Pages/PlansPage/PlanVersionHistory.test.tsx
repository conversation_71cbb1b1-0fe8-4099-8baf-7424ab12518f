// src/Pages/PlansPage/PlanVersionHistory.test.tsx
import {fireEvent, screen} from '@testing-library/react';
import {memoryRenderWithTheme} from 'TestHelper/TestHelper';
import {describe, expect, it} from 'vitest';
import PlanVersionHistory from './PlanVersionHistory';

describe('PlanVersionHistory', () => {
  const basePlan: any = {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2024-01-01',
    modifiedOn: null,
    createdBy: 'user1',
    modifiedBy: null,
    id: 'plan1',
    name: 'Test Plan',
    description: null,
    tier: 'Standard',
    size: null,
    price: '100',
    status: 'ACTIVE',
    version: '1',
    allowedUnlimitedUsers: false,
    costPerUser: '10',
    billingCycleId: 'bc1',
    currencyId: 'cur1',
    configureDeviceId: 'cd1',
    planSizeId: 'ps1',
    billingCycle: undefined,
    currency: undefined,
    configureDevice: undefined,
    planSize: undefined,
  };

  const injectedValues = {
    ...basePlan,
    planHistories: [
      {
        id: 'h1',
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '2024-01-02',
        modifiedOn: null,
        createdBy: 'user2',
        modifiedBy: null,
        price: 90,
        version: '0',
        allowedUnlimitedUsers: false,
        costPerUser: 9,
        planId: 'plan1',
        plan: 'Test Plan',
      },
      {
        id: 'h2',
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '2024-01-03',
        modifiedOn: null,
        createdBy: 'user3',
        modifiedBy: null,
        price: 80,
        version: '00',
        allowedUnlimitedUsers: false,
        costPerUser: 8,
        planId: 'plan1',
        plan: 'Test Plan',
      },
    ],
  };

  it('renders the header', () => {
    memoryRenderWithTheme(<PlanVersionHistory />);
    expect(screen.getByText('Plan version history')).toBeInTheDocument();
  });

  it('expands and renders PlanHistory when clicked', () => {
    memoryRenderWithTheme(<PlanVersionHistory injectedValues={injectedValues} />);
    const header = screen.getByText('Plan version history');
    fireEvent.click(header);
    expect(screen.getByText('Plan version history')).toBeInTheDocument();
    // Should render at least one version (head + histories)
    const planHead = screen.getAllByText('Test Plan');
    expect(planHead.length).toBeGreaterThan(0);
  });

  it('collapses when clicked again', () => {
    memoryRenderWithTheme(<PlanVersionHistory injectedValues={injectedValues} />);
    const header = screen.getByText('Plan version history');
    fireEvent.click(header); // expand
    fireEvent.click(header); // collapse
    // Should not render any visible Test Plan after collapse
    const planHead = screen.queryAllByText('Test Plan');
    planHead.forEach(node => {
      expect(node).not.toBeVisible();
    });
  });

  it('respects minExpandedHeight prop', () => {
    memoryRenderWithTheme(<PlanVersionHistory injectedValues={injectedValues} minExpandedHeight="200px" />);
    const header = screen.getByText('Plan version history');
    fireEvent.click(header);
    // Should render at least one version (head + histories)
    const planHead = screen.getAllByText('Test Plan');
    expect(planHead.length).toBeGreaterThan(0);
  });

  it('renders correctly with empty planHistories', () => {
    memoryRenderWithTheme(<PlanVersionHistory injectedValues={{...basePlan, planHistories: []}} />);
    const header = screen.getByText('Plan version history');
    fireEvent.click(header);
    // Should render only the head, not any history
    const planHead = screen.getAllByText('Test Plan');
    expect(planHead.length).toBe(1);
  });
});
