import {useSnackbar} from 'notistack';
import {useState} from 'react';
import {useUpdateStatusMutation} from 'redux/app/planManagementAPiSlice';
import {PlanStatus} from '../plans.utils';
import {ActionTypes} from './ActivateDeactivateDialog';

interface IUseActivationDeactivationState {
  refetchPlans: () => void;
}

/**
 * Custom React hook to manage the activation and deactivation state of plans.
 *
 * This hook provides state and handlers for opening/closing the activation/deactivation dialog,
 * tracking the current action type, and performing the activation or deactivation of a plan.
 * It also handles showing notifications upon success or failure and triggers a refetch of plans after status updates.
 *
 * @param {IUseActivationDeactivationState} params - The parameters for the hook.
 * @param {() => void} params.refetchPlans - Callback function to refetch the list of plans after a status update.
 *
 * @returns {{
 *   openActivateDeactivate: boolean;
 *   setOpenActivateDeactivate: React.Dispatch<React.SetStateAction<boolean>>;
 *   actionType: ActionTypes | null;
 *   setActionType: React.Dispatch<React.SetStateAction<ActionTypes | null>>;
 *   isUpdatingStatus: boolean;
 *   handleActivateDeactivate: (action: ActionTypes, planId: string, planName: string) => void;
 * }} An object containing state variables and handlers for managing plan activation/deactivation.
 */
export const useActivationDeactivationState = ({refetchPlans}: IUseActivationDeactivationState) => {
  const {enqueueSnackbar} = useSnackbar();

  const [updateStatus, {isLoading: isUpdatingStatus}] = useUpdateStatusMutation();
  const [openActivateDeactivate, setOpenActivateDeactivate] = useState(false);
  const [actionType, setActionType] = useState<ActionTypes | null>(null);
  const handleActivateDeactivate = (action: ActionTypes, planId: string, planName: string) => {
    updateStatus({planId: planId, status: action === ActionTypes.Activate ? PlanStatus.ACTIVE : PlanStatus.INACTIVE})
      .then(result => {
        if (result.error) {
          setOpenActivateDeactivate(false);
          return;
        }
        const actionType = action === ActionTypes.Activate ? 'activated' : 'deactivated';
        enqueueSnackbar(`Plan ${actionType} successfully!`, {variant: 'success', subMessage: planName});
        setOpenActivateDeactivate(false);
        refetchPlans();
      })
      .catch(() => {});
  };

  return {
    openActivateDeactivate,
    setOpenActivateDeactivate,
    actionType,
    setActionType,
    isUpdatingStatus,
    handleActivateDeactivate,
  };
};
