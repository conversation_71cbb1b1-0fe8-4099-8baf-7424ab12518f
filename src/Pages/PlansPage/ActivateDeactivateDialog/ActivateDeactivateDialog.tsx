import {Box, styled, Typography} from '@mui/material';
import ActivateConfirmationIcon from 'Assets/confirmation-icons/ActivateConfirmIcon';
import DeactivateConfirmIcon from 'Assets/confirmation-icons/DeactivateConfirmIcon';
import BorderButton from 'Components/BorderButton/BorderButton';
import Button from 'Components/Button';
import {DefaultDialog} from 'Components/DefaultDialog/DefaultDialog';
import {Integers} from 'Helpers/integers';

interface ActivateDeactivateDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  actionType: 'Activate' | 'Deactivate';
  title: string;
  isLoading: boolean;
}

export const IconWrapper = styled(Box)(() => ({
  display: 'flex',
  justifyContent: 'center',
}));

export const KeyIconContainer = styled(Box)(({theme}) => {
  return {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '5rem',
    height: '5rem',
    borderRadius: '50%',
    border: `1px solid ${theme.palette.white[Integers.TwoHundred]}`,
    padding: '1rem',
    backgroundColor: theme.palette.alert.error.bg,
    fill: 'transparent',
    color: theme.palette.alert.success.bg,
  };
});

/**
 * Enum representing the possible actions for plan activation state.
 *
 * @enum {string}
 * @property {string} Activate - Indicates the action to activate a plan.
 * @property {string} Deactivate - Indicates the action to deactivate a plan.
 */
export enum ActionTypes {
  Activate = 'Activate',
  Deactivate = 'Deactivate',
}

/**
 * A dialog component for confirming activation or deactivation of a plan.
 *
 * Displays a confirmation message, an icon based on the action type, and the plan title.
 * Provides "Cancel" and "Activate"/"Deactivate" buttons for user actions.
 *
 * @param open - Whether the dialog is open.
 * @param onClose - Callback fired when the dialog is requested to be closed.
 * @param onConfirm - Callback fired when the user confirms the action.
 * @param actionType - The type of action, either 'Activate' or 'Deactivate'.
 * @param title - The title of the plan to be shown in the dialog.
 */
export const ActivateDeactivateDialog = ({
  open,
  onClose,
  onConfirm,
  actionType,
  title,
  isLoading,
}: ActivateDeactivateDialogProps) => {
  const icon =
    actionType === ActionTypes.Activate ? (
      <ActivateConfirmationIcon sx={{fill: 'transparent'}} />
    ) : (
      <DeactivateConfirmIcon sx={{fill: 'transparent'}} />
    );
  const actionMessage =
    actionType === ActionTypes.Activate
      ? 'Are you sure you want to activate this plan?'
      : 'Are you sure you want to deactivate this plan?';

  return (
    <DefaultDialog title={actionType + ' Plan'} maxWidth={400} open={open} onClose={onClose}>
      <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4}}>
        <IconWrapper>
          <KeyIconContainer>{icon}</KeyIconContainer>
        </IconWrapper>

        <Typography sx={{mt: 2, fontSize: '1.125rem', fontWeight: 700, color: 'body.dark', textAlign: 'center'}}>
          {actionMessage}
        </Typography>
        <Typography
          sx={{
            mt: 1,
            fontSize: '0.75rem',
            fontWeight: 700,
            backgroundColor: 'secondary.50',
            borderWidth: '0.0625rem',
            borderStyle: 'solid',
            borderColor: 'secondary.50',
            padding: '3px 10px',
            borderRadius: '4px',
            color: 'secondary.700',
            textAlign: 'center',
          }}
        >
          {title}
        </Typography>
        <Box
          sx={{mt: 4, display: 'flex', gap: 1, flexDirection: 'row', width: '100%', fontSize: '1rem', fontWeight: 600}}
        >
          <BorderButton sx={{flex: 1}} onClick={onClose}>
            Cancel
          </BorderButton>
          <Button
            variant="contained"
            isLoading={isLoading}
            sx={{
              borderColor: 'body.100',
              borderRadius: '0.375rem',
              color: isLoading ? 'primary' : 'other.white',
              fontWeight: 'inherit',
              fontSize: 'inherit',
              backgroundColor: 'secondary.main',
              flex: 1,
            }}
            onClick={onConfirm}
            data-testid={actionType === 'Activate' ? 'dialog-activate-button' : 'dialog-deactivate-button'}
          >
            {actionType}
          </Button>
        </Box>
      </Box>
    </DefaultDialog>
  );
};
