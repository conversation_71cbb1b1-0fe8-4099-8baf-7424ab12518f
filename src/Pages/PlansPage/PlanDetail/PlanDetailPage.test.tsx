// src/Pages/PlansPage/PlanDetail/PlanDetailPage.test.tsx

import {fireEvent, screen} from '@testing-library/react';
import {useNavigate} from 'react-router-dom';
import {memoryRenderWithTheme} from 'TestHelper/TestHelper';
import {beforeEach, describe, expect, it, Mock, vi} from 'vitest';
import PlanDetailPage from './PlanDetailPage';

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(),
    useParams: () => ({planId: 'test-plan-id'}),
  };
});

vi.mock('Components/PermissionRedirectWrapper/PermissionProvider', () => ({
  usePermissions: () => ({
    hasPermission: () => true,
  }),
}));

vi.mock('redux/app/planManagementAPiSlice', async importOriginal => {
  const actual = await importOriginal();
  return {
    ...(actual as Record<string, any>),
    useGetPlanByIdQuery: () => ({
      data: {
        id: 'test-plan-id',
        name: 'Test Plan',
        status: 'ACTIVE',
        version: 'v1.0',
        price: 100,
        billingCycle: {cycleName: 'monthly'},
        configureDevice: {min: 1, max: 10},
        tier: 'basic',
        costPerUser: 10,
        createdOn: '2023-01-01',
        modifiedOn: '2023-01-02',
      },
      isLoading: false,
      error: undefined,
      refetch: vi.fn(),
    }),
    useUpdatePlanByIdMutation: () => [vi.fn(), {isLoading: false}],
    useUpdateStatusMutation: () => [vi.fn(), {isLoading: false}],
  };
});

vi.mock('../PlanStatus', () => ({
  __esModule: true,
  default: ({status}: {status: string}) => <span data-testid="plan-status">{status}</span>,
}));

vi.mock('../PlanVersionHistory', () => ({
  __esModule: true,
  default: () => <div data-testid="plan-version-history" />,
}));

vi.mock('Components/Breadcrumb/Breadcrumb', () => ({
  __esModule: true,
  default: () => <nav data-testid="breadcrumb" />,
}));

vi.mock('Components/BorderButton/BorderButton', () => ({
  __esModule: true,
  default: ({children, ...props}: any) => (
    <button {...props} data-testid="border-button">
      {children}
    </button>
  ),
}));

vi.mock('Components/PermissionWrapper', () => ({
  __esModule: true,
  default: ({children}: {children: React.ReactNode}) => <>{children}</>,
}));

describe('PlanDetailPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders plan details', () => {
    memoryRenderWithTheme(<PlanDetailPage />);
    expect(screen.getByText('Test Plan')).toBeInTheDocument();
    expect(screen.getByTestId('plan-status')).toHaveTextContent('ACTIVE');
    expect(screen.getByText('Plan Information')).toBeInTheDocument();
    expect(screen.getByTestId('plan-version-history')).toBeInTheDocument();
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument();
  });

  it('renders close button and triggers navigation', () => {
    const mockNavigate = vi.fn();
    (useNavigate as Mock).mockReturnValue(mockNavigate);
    memoryRenderWithTheme(<PlanDetailPage />);
    const closeBtn = screen.getAllByTestId('border-button').find(btn => btn.textContent?.includes('Close'));
    expect(closeBtn).toBeInTheDocument();
    fireEvent.click(closeBtn!);
    expect(mockNavigate).toHaveBeenCalled();
  });

  it('shows loading state', async () => {
    vi.resetModules();
    vi.doMock('redux/app/planManagementAPiSlice', () => ({
      useGetPlanByIdQuery: () => ({
        data: undefined,
        isLoading: true,
        error: undefined,
        refetch: vi.fn(),
      }),
      useUpdatePlanByIdMutation: () => [vi.fn(), {isLoading: false}],
      useUpdateStatusMutation: () => [vi.fn(), {isLoading: false}],
    }));
    vi.doMock('Components/CenterLoaderContainer', () => ({
      __esModule: true,
      default: ({isLoading}: {isLoading: boolean}) => (isLoading ? <div data-testid="circular-progress" /> : null),
    }));
    const {default: PlanDetailPageReloaded} = await import('./PlanDetailPage');
    memoryRenderWithTheme(<PlanDetailPageReloaded />);
    expect(screen.getByTestId('circular-progress')).toBeInTheDocument();
  });

  it('shows error state', async () => {
    vi.resetModules();
    vi.doMock('redux/app/planManagementAPiSlice', () => ({
      useGetPlanByIdQuery: () => ({
        data: undefined,
        isLoading: false,
        error: true,
        refetch: vi.fn(),
      }),
      useUpdatePlanByIdMutation: () => [vi.fn(), {isLoading: false}],
      useUpdateStatusMutation: () => [vi.fn(), {isLoading: false}],
    }));
    const {default: PlanDetailPageReloaded} = await import('./PlanDetailPage');
    memoryRenderWithTheme(<PlanDetailPageReloaded />);
    // Use a function matcher to handle possible element splitting
    expect(screen.getByText(content => content.includes('Failed to load plan details.'))).toBeInTheDocument();
  });
});
