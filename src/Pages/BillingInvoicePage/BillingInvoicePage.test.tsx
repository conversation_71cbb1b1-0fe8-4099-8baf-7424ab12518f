// @vitest-environment jsdom
import {ThemeProvider, createTheme} from '@mui/material/styles';
import '@testing-library/jest-dom';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import React from 'react';
import {vi} from 'vitest';
import BillingInvoice, {ActionButtons} from './BillingInvoicePage';
import {
  InvoiceStatus,
  billingTableColumns,
  getBackendColumnName,
  getFontColor,
  getIndicatorColor,
  getStatusColor,
  getStatusLabel,
} from './billings.utils';

// Robust theme mock for all palette keys used in BillingInvoicePage and dialogs
const robustTheme = createTheme({
  palette: {
    body: {
      dark: '#222',
      100: '#f5f5f5',
      200: '#eeeeee',
      400: '#bdbdbd',
      500: '#9e9e9e',
      800: '#424242',
    },
    white: {
      100: '#fff',
      200: '#f8f8f8',
    },
    black: {
      main: '#000',
    },
    primary: {main: '#1976d2'},
    secondary: {main: '#dc004e'},
    alert: {
      success: {
        main: '#4caf50',
        bg: '#e0ffe0',
        border: '#388e3c',
        onBg: '#e0ffe0',
      },
      error: {
        main: '#f44336',
        bg: '#ffebee',
        border: '#d32f2f',
        onBg: '#ffebee',
      },
      warning: {
        main: '#ff9800',
        bg: '#fff3e0',
        border: '#f57c00',
        onBg: '#fff3e0',
      },
      info: {
        main: '#2196f3',
        bg: '#e3f2fd',
        border: '#1976d2',
        onBg: '#e3f2fd',
      },
    },
  },
});
vi.mock('@mui/material/styles', async importOriginal => {
  const actual = await importOriginal();
  return Object.assign({}, actual, {
    useTheme: () => robustTheme,
  });
});
function renderWithTheme(ui: React.ReactElement) {
  return render(<ThemeProvider theme={robustTheme}>{ui}</ThemeProvider>);
}

// Mocks
const mockGetTenantBillingsQuery = vi.fn();
const mockGetTenantBillingsCountQuery = vi.fn();
const mockEnqueueSnackbar = vi.fn();
const mockUseGetInvoiceStatusQuery = vi.fn().mockReturnValue({
  data: {statuses: {PAID: 'Paid', UNPAID: 'Unpaid'}},
  isLoading: false,
  error: undefined,
});
const mockNavigate = vi.fn();

const mockUseDownloadInvoiceByIdQuery = vi.fn();

vi.mock('redux/app/invoiceManagementApiSlice', () => ({
  useGetTenantBillingsQuery: (...args: any[]) => mockGetTenantBillingsQuery(...args),
  useGetTenantBillingsCountQuery: (...args: any[]) => mockGetTenantBillingsCountQuery(...args),
  useGetInvoiceStatusQuery: (...args: any[]) => mockUseGetInvoiceStatusQuery(...args),
  useDownloadInvoiceByIdQuery: (...args: any[]) => mockUseDownloadInvoiceByIdQuery(...args),
}));
vi.mock('notistack', () => ({
  useSnackbar: () => ({
    enqueueSnackbar: mockEnqueueSnackbar,
  }),
}));
vi.mock('react-router-dom', () => ({
  useLocation: () => vi.fn(),
  useNavigate: () => mockNavigate,
}));
vi.mock('Components/Table', () => ({
  Table: (props: any) => (
    <div data-testid={props['data-testid'] || 'billings-table'}>
      {props.data && props.data.length > 0
        ? props.data.map((row: any, idx: number) => (
            <div key={row.id} data-testid="billing-row">
              {row.name}
              {props.columns && props.columns[0] && (
                <span data-testid="action-buttons">
                  {props.columns[0].cell && props.columns[0].cell({row: {original: row, row: {original: row}}})}
                </span>
              )}
            </div>
          ))
        : 'No Data'}
      <button data-testid="sort-btn" onClick={() => props.onSortChange && props.onSortChange('createdDate', true)}>
        Sort
      </button>
      <button data-testid="page-btn" onClick={() => props.onPageChange && props.onPageChange(2)}>
        Page 2
      </button>
      <button data-testid="rows-btn" onClick={() => props.onRowsPerPageChange && props.onRowsPerPageChange(50)}>
        Rows 50
      </button>
    </div>
  ),
  DebouncedInput: (props: any) => (
    <input
      data-testid={props['data-testid'] || 'search-billings'}
      value={props.value}
      onChange={e => props.onChange?.(e.target.value)}
      placeholder={props.placeholder}
    />
  ),
}));
vi.mock('Components/BorderButton/BorderButton', () => ({
  __esModule: true,
  default: React.forwardRef((props: any, ref: any) => (
    <button data-testid="filter-btn" onClick={props.onClick} ref={ref}>
      {props.children}
    </button>
  )),
}));
vi.mock('Components/SVGImageFromPath', () => ({
  __esModule: true,
  default: () => <span data-testid="svg-icon" />,
}));
vi.mock('Assets/EyeIcon', () => ({
  __esModule: true,
  default: (props: any) => (
    <button data-testid="eye-icon" onClick={props.onClick}>
      View
    </button>
  ),
}));
vi.mock('Assets/DownloadIcon', () => ({
  __esModule: true,
  default: (props: any) => (
    <button data-testid="download-icon" {...props}>
      Download
    </button>
  ),
}));

describe('BillingInvoicePage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Default mock for download hook to prevent destructuring errors
    mockUseDownloadInvoiceByIdQuery.mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: false,
      refetch: vi.fn(),
      fulfilledTimeStamp: Date.now(),
      status: 'uninitialized',
    });
  });

  it('shows loader when loading', () => {
    mockGetTenantBillingsQuery.mockReturnValue({isFetching: true});
    mockGetTenantBillingsCountQuery.mockReturnValue({isFetching: true});
    renderWithTheme(<BillingInvoice />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
  it('shows error snackbar when countError only', async () => {
    mockGetTenantBillingsQuery.mockReturnValue({isFetching: false, error: false});
    mockGetTenantBillingsCountQuery.mockReturnValue({isFetching: false, error: true});
    renderWithTheme(<BillingInvoice />);
    await waitFor(() =>
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith('Failed to fetch billings count', {variant: 'error'}),
    );
  });

  it('shows error snackbar when billingsError or countError', async () => {
    mockGetTenantBillingsQuery.mockReturnValue({isFetching: false, error: true});
    mockGetTenantBillingsCountQuery.mockReturnValue({isFetching: false, error: false});
    renderWithTheme(<BillingInvoice />);
    await waitFor(() =>
      expect(mockEnqueueSnackbar).toHaveBeenCalledWith('Failed to fetch billings data', {variant: 'error'}),
    );
  });

  it('renders table with billing data and action buttons', () => {
    const data = [{id: '1', name: 'Invoice 1'}];
    mockGetTenantBillingsQuery.mockReturnValue({isFetching: false, data});
    mockGetTenantBillingsCountQuery.mockReturnValue({isFetching: false, data: {count: 1}});
    renderWithTheme(<BillingInvoice />);
    expect(screen.getByTestId('billing-row')).toBeInTheDocument();
    expect(screen.getByTestId('action-buttons')).toBeInTheDocument();
  });

  it('ActionButtons: triggers navigation on view', () => {
    const row = {row: {original: {id: '123'}}};
    renderWithTheme(<ActionButtons row={row as any} />);
    fireEvent.click(screen.getByTestId('eye-icon'));
    expect(mockNavigate).toHaveBeenCalledWith('/invoices/123', {
      state: {
        billingCustomerId: undefined,
        fromOrigin: 'billing-invoices',
        invoiceId: '123',
        tenantId: undefined,
        tenantName: undefined,
      },
    });
  });

  it('ActionButtons: renders download and view all invoices', () => {
    const row = {row: {original: {id: '123'}}};
    renderWithTheme(<ActionButtons row={row as any} />);
    expect(screen.getByTestId('download-icon')).toBeInTheDocument();
    expect(screen.getByTestId('view-all-invoices-icon')).toBeInTheDocument();
  });

  it('searches billings', () => {
    mockGetTenantBillingsQuery.mockReturnValue({isFetching: false, data: []});
    mockGetTenantBillingsCountQuery.mockReturnValue({isFetching: false, data: {count: 0}});
    renderWithTheme(<BillingInvoice />);
    const searchInput = screen.getByTestId('search-billings');
    fireEvent.change(searchInput, {target: {value: 'test'}});
    expect(searchInput).toHaveValue('test');
  });

  it('handles sort, page, and rows-per-page changes', () => {
    mockGetTenantBillingsQuery.mockReturnValue({isFetching: false, data: []});
    mockGetTenantBillingsCountQuery.mockReturnValue({isFetching: false, data: {count: 0}});
    renderWithTheme(<BillingInvoice />);
    fireEvent.click(screen.getByTestId('sort-btn'));
    fireEvent.click(screen.getByTestId('page-btn'));
    fireEvent.click(screen.getByTestId('rows-btn'));
    // No assertion needed, just coverage for handlers
  });

  it('opens and closes filter', () => {
    mockGetTenantBillingsQuery.mockReturnValue({isFetching: false, data: []});
    mockGetTenantBillingsCountQuery.mockReturnValue({isFetching: false, data: {count: 0}});
    renderWithTheme(<BillingInvoice />);
    fireEvent.click(screen.getByTestId('filter-btn'));
    // No assertion, just coverage for filter open/close
  });
  describe('BillingInvoicePage utils and table columns', () => {
    it('covers all InvoiceStatus branches in color/label utils', () => {
      // All enum values + an invalid one
      const statuses = [
        InvoiceStatus.DRAFT,
        InvoiceStatus.OPEN,
        InvoiceStatus.PAID,
        InvoiceStatus.VOID,
        InvoiceStatus.UNCOLLECTIBLE,
        'invalid' as InvoiceStatus,
      ];
      statuses.forEach(status => {
        getStatusColor(status);
        getFontColor(status);
        getIndicatorColor(status);
        getStatusLabel(status);
      });
    });

    it('covers getBackendColumnName for all keys and fallback', () => {
      expect(getBackendColumnName('tenantName')).toBe('tenantName');
      expect(getBackendColumnName('invoiceId')).toBe('invoiceId');
      expect(getBackendColumnName('amount')).toBe('amount');
      expect(getBackendColumnName('invoiceStatus')).toBe('invoiceStatus');
      expect(getBackendColumnName('createdDate')).toBe('createdOn');
      expect(getBackendColumnName('dueDate')).toBe('dueDate');
      expect(getBackendColumnName('unknown')).toBe('unknown');
    });

    it('covers billingTableColumns cell renderers', () => {
      // Status cell
      const statusCell = billingTableColumns.find((c: any) => c.id === 'invoiceStatus')?.cell;
      if (statusCell) {
        Object.values(InvoiceStatus).forEach(status => {
          statusCell({getValue: () => status} as any);
        });
        // Fallback
        statusCell({getValue: () => 'invalid'} as any);
      }
      // Invoice date cell
      const dateCell = billingTableColumns.find((c: any) => c.id === 'createdDate')?.cell;
      if (dateCell) {
        const row = {
          tenantName: 'Tenant',
          invoiceId: 'INV-1',
          amount: '100',
          invoiceStatus: InvoiceStatus.PAID,
          createdOn: '2023-08-24T00:00:00Z',
          dueDate: '2023-08-25T00:00:00Z',
        };
        dateCell({row: {original: row}} as any);
      }
      // Due date cell
      const dueDateCell = billingTableColumns.find((c: any) => c.id === 'dueDate')?.cell;
      if (dueDateCell) {
        const rowWithDueDate = {
          tenantName: 'Tenant',
          invoiceId: 'INV-1',
          amount: '100',
          invoiceStatus: InvoiceStatus.PAID,
          createdOn: '2023-08-24T00:00:00Z',
          dueDate: '2023-08-25T00:00:00Z',
        };
        const rowWithoutDueDate = {
          tenantName: 'Tenant',
          invoiceId: 'INV-1',
          amount: '100',
          invoiceStatus: InvoiceStatus.PAID,
          createdOn: '2023-08-24T00:00:00Z',
          dueDate: undefined,
        };
        dueDateCell({row: {original: rowWithDueDate}} as any);
        dueDateCell({row: {original: rowWithoutDueDate}} as any);
      }
      // Actions cell
      const actionsCell = billingTableColumns.find((c: any) => c.header === 'Actions')?.cell;
      if (actionsCell) {
        actionsCell({} as any);
      }
    });
  });

  describe('ActionButtons Download Functionality', () => {
    beforeEach(() => {
      vi.clearAllMocks();
      // Default mock for download hook
      mockUseDownloadInvoiceByIdQuery.mockReturnValue({
        data: undefined,
        error: undefined,
        isLoading: false,
        refetch: vi.fn(),
        fulfilledTimeStamp: Date.now(),
        status: 'uninitialized',
      });
    });

    it('shows loader and triggers download on DownloadIcon click', async () => {
      // Mock document.createElement for anchor element
      const mockClick = vi.fn();
      const originalCreateElement = document.createElement;
      vi.spyOn(document, 'createElement').mockImplementation((tag: string) => {
        if (tag === 'a') {
          const anchor = originalCreateElement.call(document, 'a') as HTMLAnchorElement;
          anchor.click = mockClick;
          anchor.remove = vi.fn();
          return anchor;
        }
        return originalCreateElement.call(document, tag);
      });

      const row = {row: {original: {id: 'inv-1', invoiceId: 'stripe-1'}}};
      renderWithTheme(<ActionButtons row={row as any} />);

      // Initially, download icon should be visible
      const downloadIcon = screen.getByTestId('download-icon');
      expect(downloadIcon).toBeInTheDocument();

      // Click the download icon
      fireEvent.click(downloadIcon);

      // Loader should appear immediately after click (because isDownloading is set to true)
      await waitFor(() => {
        expect(screen.getAllByRole('progressbar').length).toBeGreaterThan(0);
      });

      // Verify the download icon is no longer visible (replaced by loader)
      expect(screen.queryByTestId('download-icon')).not.toBeInTheDocument();
    });

    it('handles download error and shows error snackbar', async () => {
      // Mock the download API hook to return an error
      mockUseDownloadInvoiceByIdQuery.mockImplementation((_params, options) => {
        if (options?.skip) {
          return {
            data: undefined,
            error: undefined,
            isLoading: false,
            refetch: vi.fn(),
            fulfilledTimeStamp: Date.now(),
            status: 'uninitialized',
          };
        }
        return {
          data: undefined,
          error: {message: 'Download failed'},
          isLoading: false,
          refetch: vi.fn(),
          fulfilledTimeStamp: Date.now(),
          status: 'rejected',
        };
      });

      const row = {row: {original: {id: 'inv-1', invoiceId: 'stripe-1'}}};
      renderWithTheme(<ActionButtons row={row as any} />);

      const downloadIcon = screen.getByTestId('download-icon');
      fireEvent.click(downloadIcon);

      // Wait for error handling
      await waitFor(() => {
        expect(mockEnqueueSnackbar).toHaveBeenCalledWith('Failed to download invoice', {variant: 'error'});
      });

      // Download icon should be visible again (not loading)
      expect(screen.getByTestId('download-icon')).toBeInTheDocument();
    });

    it('renders download icon when not loading', () => {
      const row = {row: {original: {id: 'inv-1', invoiceId: 'stripe-1'}}};
      renderWithTheme(<ActionButtons row={row as any} />);

      expect(screen.getByTestId('download-icon')).toBeInTheDocument();
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    it('shows loading spinner when API is loading', () => {
      // Mock the download API hook to return loading state
      mockUseDownloadInvoiceByIdQuery.mockReturnValue({
        data: undefined,
        error: undefined,
        isLoading: true,
        refetch: vi.fn(),
        fulfilledTimeStamp: Date.now(),
        status: 'pending',
      });

      const row = {row: {original: {id: 'inv-1', invoiceId: 'stripe-1'}}};
      renderWithTheme(<ActionButtons row={row as any} />);

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.queryByTestId('download-icon')).not.toBeInTheDocument();
    });
  });
});
