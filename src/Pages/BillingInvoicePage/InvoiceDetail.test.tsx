// InvoiceDetail.test.tsx
vi.mock('notistack', async () => {
  const actual = await vi.importActual<typeof import('notistack')>('notistack');
  return {
    ...actual,
    useSnackbar: vi.fn(),
  };
});
import {ThemeProvider, createTheme} from '@mui/material/styles';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import {SnackbarProvider, useSnackbar} from 'notistack';
import {MemoryRouter} from 'react-router-dom';
import * as reduxApi from 'redux/app/invoiceManagementApiSlice';
import {vi} from 'vitest';
import InvoiceDetail from './InvoiceDetail';

const AllProviders = ({children}: {children: React.ReactNode}) => (
  <ThemeProvider
    theme={createTheme({
      palette: {
        body: {
          100: '#f0f0f0',
          200: '#e0e0e0',
          300: '#d0d0d0',
          500: '#b0b0b0',
          800: '#505050',
          900: '#202020',
          dark: '#222222',
          main: '#000000',
        },
        black: {
          main: '#000000',
        },
        secondary: {
          main: '#1976d2',
          25: '#f5f5f5',
          100: '#eeeeee',
          200: '#cccccc',
          900: '#111111',
        },
      },
    })}
  >
    <SnackbarProvider>
      <MemoryRouter>{children}</MemoryRouter>
    </SnackbarProvider>
  </ThemeProvider>
);

// Mocks
let mockLocationState: any = {
  tenantName: 'TenantX',
  invoiceId: 'INV-1',
  tenantId: 'TID-1',
  billingCustomerId: 'BILL-1',
  fromOrigin: 'billing-invoices',
};
const mockNavigate = vi.fn();
vi.mock('react-router', () => ({
  ...vi.importActual('react-router'),
  useNavigate: () => mockNavigate,
  useLocation: vi.fn(() => ({
    state: mockLocationState,
  })),
}));

vi.mock('redux/app/invoiceManagementApiSlice', () => ({
  useGetInvoiceByIdQuery: vi.fn(),
  useDownloadInvoiceByIdQuery: vi.fn(),
}));

/* removed: do not destructure enqueueSnackbar at the top */

const invoiceData = {
  invoiceId: 'INV-1',
  invoiceStatus: 'paid',
  createdOn: '2023-08-01',
  dueDate: '2023-08-10',
  startDate: '2023-07-01',
  endDate: '2023-07-31',
  amount: 123.45,
};

describe('InvoiceDetail', () => {
  const enqueueSnackbar = vi.fn();
  beforeEach(() => {
    vi.clearAllMocks();
    (useSnackbar as any).mockReturnValue({enqueueSnackbar});
    // Default mock for download hook to prevent destructuring errors
    (reduxApi.useDownloadInvoiceByIdQuery as any).mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: false,
      refetch: vi.fn(),
      fulfilledTimeStamp: Date.now(),
      status: 'uninitialized',
    });
  });

  it('renders loader when loading', () => {
    (reduxApi.useGetInvoiceByIdQuery as any).mockReturnValue({
      isLoading: true,
    });
    render(<InvoiceDetail />, {wrapper: AllProviders});
    expect(screen.getByTestId('circularProgress')).toBeInTheDocument();
  });

  it('shows snackbar on invoice error', async () => {
    (reduxApi.useGetInvoiceByIdQuery as any).mockReturnValue({
      isLoading: false,
      error: true,
    });
    render(<InvoiceDetail />, {wrapper: AllProviders});
    await waitFor(() => {
      // Check that enqueueSnackbar was called at least once
      expect(enqueueSnackbar).toHaveBeenCalled();
      // Optionally, check that one of the calls matches the error message
      const calledWithError = enqueueSnackbar.mock.calls.some(
        (args: any[]) => args[0] === 'Failed to fetch invoice data' && args[1]?.variant === 'error',
      );
      expect(calledWithError).toBe(true);
    });
  });

  it('renders invoice details and handles navigation', () => {
    (reduxApi.useGetInvoiceByIdQuery as any).mockReturnValue({
      isLoading: false,
      data: invoiceData,
    });
    render(<InvoiceDetail />, {wrapper: AllProviders});
    // Top part
    expect(screen.getAllByText('TenantX').length).toBeGreaterThan(0);
    // Status chip: look for the chip by test id and check its label span
    const statusChip = screen.getByTestId('status-chip');
    const labelSpan = statusChip.querySelector('.MuiChip-label');
    // Check for the status chip's label "PAID" in the document
    expect(screen.getByText(/PAID/i)).toBeInTheDocument();
    expect(screen.getByTestId('view-all-invoices')).toBeInTheDocument();
    expect(screen.getByTestId('download-invoice')).toBeInTheDocument();

    // Breadcrumbs
    expect(screen.getByText('Billings & Invoices')).toBeInTheDocument();

    // Invoice info
    expect(screen.getByText('Invoice no.')).toBeInTheDocument();
    expect(screen.getByText('INV-1')).toBeInTheDocument();
    expect(screen.getByText('Invoice Date')).toBeInTheDocument();
    expect(screen.getByText('Due Date')).toBeInTheDocument();
    expect(screen.getByText('From')).toBeInTheDocument();
    expect(screen.getByText('To')).toBeInTheDocument();
    expect(screen.getByText('Invoice amount')).toBeInTheDocument();

    // Payment details
    expect(screen.getByText('Payment details')).toBeInTheDocument();
    expect(screen.getByTestId('plan-cost')).toHaveTextContent('1 Aug, 2023');
    expect(screen.getByTestId('users-cost')).toHaveTextContent('$123.45');
    expect(screen.getByTestId('total-cost')).toHaveTextContent('$123.45');

    // Close button
    fireEvent.click(screen.getByText('Close'));
    expect(mockNavigate).toHaveBeenCalledWith(-1);

    // View all invoices navigation
    fireEvent.click(screen.getByTestId('view-all-invoices'));
    expect(mockNavigate).toHaveBeenCalledWith('/billing-invoices/TID-1', {
      state: {
        tenantName: 'TenantX',
        id: 'TID-1',
        billingCustomerId: 'BILL-1',
      },
    });
  });

  it('renders all fallback values for missing invoice fields', () => {
    (reduxApi.useGetInvoiceByIdQuery as any).mockReturnValue({
      isLoading: false,
      data: {},
    });
    render(<InvoiceDetail />, {wrapper: AllProviders});
    expect(screen.getAllByText('-').length).toBeGreaterThan(1);
    expect(screen.getByTestId('users-cost')).toHaveTextContent('$');
    expect(screen.getByTestId('total-cost')).toHaveTextContent('$');
  });
});

it('renders fallback UI when required keys are missing', () => {
  // Remove required keys from location.state
  const enqueueSnackbar = vi.fn();
  const closeSnackbar = vi.fn();
  (vi.mocked(reduxApi.useGetInvoiceByIdQuery) as any).mockReturnValue({});
  vi.mocked(useSnackbar).mockReturnValue({enqueueSnackbar, closeSnackbar});
  mockLocationState = {}; // No required keys
  render(<InvoiceDetail />, {wrapper: AllProviders});
  expect(screen.getByText(/Unable to load invoice page/i)).toBeInTheDocument();
});

it('renders correct breadcrumbs when fromOrigin is not billing-invoices', () => {
  (reduxApi.useGetInvoiceByIdQuery as any).mockReturnValue({
    isLoading: false,
    data: {
      invoiceId: 'INV-2',
      invoiceStatus: 'paid',
      createdOn: '2023-08-01',
      dueDate: '2023-08-10',
      startDate: '2023-07-01',
      endDate: '2023-07-31',
      amount: 456.78,
    },
  });
  // Patch useLocation to return fromOrigin !== 'billing-invoices'
  mockLocationState = {
    tenantName: 'TenantY',
    invoiceId: 'INV-2',
    tenantId: 'TID-2',
    billingCustomerId: 'BILL-2',
    fromOrigin: 'other-origin',
  };
  render(<InvoiceDetail />, {wrapper: AllProviders});
  expect(screen.getByText('All Invoices')).toBeInTheDocument();
  expect(screen.getAllByText('INV-2').length).toBeGreaterThan(0);
});

describe('InvoiceDetail Download Functionality', () => {
  const enqueueSnackbar = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useSnackbar as any).mockReturnValue({enqueueSnackbar});
    // Reset location state to valid values
    mockLocationState = {
      tenantName: 'TenantX',
      invoiceId: 'INV-1',
      tenantId: 'TID-1',
      billingCustomerId: 'BILL-1',
      fromOrigin: 'billing-invoices',
    };
    // Mock invoice data
    (reduxApi.useGetInvoiceByIdQuery as any).mockReturnValue({
      isLoading: false,
      data: invoiceData,
    });
  });

  it('renders download button and handles click', async () => {
    // Mock document.createElement for anchor element
    const mockClick = vi.fn();
    const originalCreateElement = document.createElement;
    vi.spyOn(document, 'createElement').mockImplementation((tag: string) => {
      if (tag === 'a') {
        const anchor = originalCreateElement.call(document, 'a') as HTMLAnchorElement;
        anchor.click = mockClick;
        anchor.remove = vi.fn();
        return anchor;
      }
      return originalCreateElement.call(document, tag);
    });

    // Default download hook mock
    (reduxApi.useDownloadInvoiceByIdQuery as any).mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: false,
      refetch: vi.fn(),
      fulfilledTimeStamp: Date.now(),
      status: 'uninitialized',
    });

    render(<InvoiceDetail />, {wrapper: AllProviders});

    // Find and click the download button
    const downloadButton = screen.getByTestId('download-invoice-button');
    expect(downloadButton).toBeInTheDocument();
    expect(downloadButton).not.toBeDisabled();

    fireEvent.click(downloadButton);

    // Button should become disabled after click (because isDownloading is set to true)
    await waitFor(() => {
      expect(downloadButton).toBeDisabled();
    });
  });

  it('shows loading state when download API is loading', () => {
    // Mock the download API hook to return loading state
    (reduxApi.useDownloadInvoiceByIdQuery as any).mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: true,
      refetch: vi.fn(),
      fulfilledTimeStamp: Date.now(),
      status: 'pending',
    });

    render(<InvoiceDetail />, {wrapper: AllProviders});

    const downloadButton = screen.getByTestId('download-invoice-button');
    expect(downloadButton).toBeDisabled();
    // Check for loading indicator in the button
    expect(downloadButton.querySelector('.MuiCircularProgress-root')).toBeInTheDocument();
  });

  it('handles download error and shows error snackbar', async () => {
    // Mock the download API hook to return an error
    (reduxApi.useDownloadInvoiceByIdQuery as any).mockImplementation((_params: any, options: any) => {
      if (options?.skip) {
        return {
          data: undefined,
          error: undefined,
          isLoading: false,
          refetch: vi.fn(),
          fulfilledTimeStamp: Date.now(),
          status: 'uninitialized',
        };
      }
      return {
        data: undefined,
        error: {message: 'Download failed'},
        isLoading: false,
        refetch: vi.fn(),
        fulfilledTimeStamp: Date.now(),
        status: 'rejected',
      };
    });

    render(<InvoiceDetail />, {wrapper: AllProviders});

    const downloadButton = screen.getByTestId('download-invoice-button');
    fireEvent.click(downloadButton);

    // Wait for error handling
    await waitFor(() => {
      expect(enqueueSnackbar).toHaveBeenCalledWith('Failed to download invoice', {variant: 'error'});
    });

    // Button should be enabled again after error
    expect(downloadButton).not.toBeDisabled();
  });

  it('renders download icon within the button', () => {
    (reduxApi.useDownloadInvoiceByIdQuery as any).mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: false,
      refetch: vi.fn(),
      fulfilledTimeStamp: Date.now(),
      status: 'uninitialized',
    });

    render(<InvoiceDetail />, {wrapper: AllProviders});

    expect(screen.getByTestId('download-icon')).toBeInTheDocument();
    expect(screen.getByText('Download Invoice')).toBeInTheDocument();
  });
});
