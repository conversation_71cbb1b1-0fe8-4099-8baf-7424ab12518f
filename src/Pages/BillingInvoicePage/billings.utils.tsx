import {Box, Typography} from '@mui/material';
import {CellContext} from '@tanstack/react-table';
import EllipsisText from 'Components/EllipsisText/EllipsisText';
import StatusChip from 'Components/StatusChip/StatusChip';
import {dateFormatter} from 'Helpers/utils';
import {ActionButtons} from './BillingInvoicePage';
import {ActionButtons as InvoiceActionButtons} from './InvoicePage';

export const DEFAULT_LIMIT = 20;
export const DEFAULT_OFFSET = 0;

const whiteMain = 'white.main';

export enum InvoiceStatus {
  DRAFT = 'draft',
  OPEN = 'open', //open means unpaid
  PAID = 'paid',
  UNCOLLECTIBLE = 'uncollectible',
  VOID = 'void',
}

export const getStatusColor = (status: InvoiceStatus): string => {
  const statusColorMap: Record<InvoiceStatus, string> = {
    [InvoiceStatus.DRAFT]: `alert.info.bg`,
    [InvoiceStatus.OPEN]: `alert.error.bg`,
    [InvoiceStatus.PAID]: `alert.success.bg`,
    [InvoiceStatus.VOID]: `tenantStatus.failed.bg`,
    [InvoiceStatus.UNCOLLECTIBLE]: `tenantStatus.failed.bg`,
  };
  return statusColorMap[status] || whiteMain;
};

export const getFontColor = (status: InvoiceStatus): string => {
  const statusColorMap: Record<InvoiceStatus, string> = {
    [InvoiceStatus.DRAFT]: `alert.info.onBg`,
    [InvoiceStatus.OPEN]: `alert.error.onBg`,
    [InvoiceStatus.PAID]: `alert.success.onBg`,
    [InvoiceStatus.VOID]: `tenantStatus.failed.onBg`,
    [InvoiceStatus.UNCOLLECTIBLE]: `tenantStatus.failed.onBg`,
  };
  return statusColorMap[status] || whiteMain;
};

export const getIndicatorColor = (status: InvoiceStatus): string => {
  const statusColorMap: Record<InvoiceStatus, string> = {
    [InvoiceStatus.DRAFT]: whiteMain,
    [InvoiceStatus.OPEN]: 'alert.error.main',
    [InvoiceStatus.PAID]: `alert.success.main`,
    [InvoiceStatus.VOID]: whiteMain,
    [InvoiceStatus.UNCOLLECTIBLE]: whiteMain,
  };
  return statusColorMap[status] || whiteMain;
};

export const getStatusLabel = (status: InvoiceStatus): string => {
  const statusLabelMap: Record<InvoiceStatus, string> = {
    [InvoiceStatus.DRAFT]: 'Draft',
    [InvoiceStatus.OPEN]: 'Unpaid',
    [InvoiceStatus.PAID]: 'Paid',
    [InvoiceStatus.VOID]: 'Void',
    [InvoiceStatus.UNCOLLECTIBLE]: 'Uncollectible',
  };
  return statusLabelMap[status] || '';
};

interface BillingTableRow {
  tenantName: string;
  invoiceId: string;
  amount: string;
  invoiceStatus: InvoiceStatus;
  createdOn: string;
  dueDate: string;
}

interface BillingTableColumn {
  header: string;
  accessorKey?: keyof BillingTableRow;
  id?: string;
  cell?: (context: CellContext<BillingTableRow, unknown>) => React.ReactNode;
}

const columnNameMap: Record<string, string> = {
  tenantName: 'tenantName',
  invoiceId: 'invoiceId',
  amount: 'amount',
  invoiceStatus: 'invoiceStatus',
  createdDate: 'createdOn',
  dueDate: 'dueDate',
};

export const getBackendColumnName = (columnName: string): string => columnNameMap[columnName] || columnName;

/**
 * Shared column definitions for the billing invoices table.
 *
 * Each column is configured with a header, accessor key, unique ID, and optional custom cell rendering logic.
 * - "Invoice no.": Displays the invoice ID.
 * - "Invoice amount": Displays the invoice amount formatted as a dollar value.
 * - "Payment status": Renders a status chip with dynamic colors and label based on the invoice status.
 * - "Invoice date": Formats and displays the invoice creation date in 'DD Month YYYY' format.
 * - "Due date": Formats and displays the invoice due date in 'DD Month YYYY' format, or '-' if not available.
 *
 * @type {BillingTableColumn[]}
 */
export const sharedTableColumns: BillingTableColumn[] = [
  {
    header: 'Invoice no.',
    accessorKey: 'invoiceId',
    id: 'invoiceId',
    cell: ({row}: CellContext<BillingTableRow, unknown>) => (
      <EllipsisText text={row.original.invoiceId && row.original.invoiceId.length > 0 ? row.original.invoiceId : '-'} />
    ),
  },
  {
    header: 'Invoice amount',
    accessorKey: 'amount',
    id: 'amount',
    cell: ({row}: CellContext<BillingTableRow, unknown>) => (
      <EllipsisText text={row.original.amount && row.original.amount.length > 0 ? `$${row.original.amount}` : '-'} />
    ),
  },
  {
    header: 'Payment status',
    accessorKey: 'invoiceStatus',
    id: 'invoiceStatus',
    cell: (context: CellContext<BillingTableRow, unknown>) => {
      const status = context.getValue() as InvoiceStatus;
      const backgroundColor = getStatusColor(status);
      const color = getFontColor(status);
      const indicatorColor = getIndicatorColor(status);
      const label = getStatusLabel(status);
      return (
        <StatusChip label={label} backgroundColor={backgroundColor} indicatorColor={indicatorColor} color={color} />
      );
    },
  },
  {
    header: 'Invoice date',
    accessorKey: 'createdOn',
    id: 'createdDate',
    cell: ({row}: CellContext<BillingTableRow, unknown>) => dateFormatter(row.original.createdOn),
  },
  {
    header: 'Due date',
    accessorKey: 'dueDate',
    id: 'dueDate',
    cell: ({row}: CellContext<BillingTableRow, unknown>) => dateFormatter(row.original.dueDate),
  },
];

/**
 * Defines the columns for the billing table, including tenant name, shared columns,
 * and an actions column with custom action buttons.
 *
 * @remarks
 * - The columns array is composed of a static "Tenant name" column, spread-in shared columns,
 *   and a custom "Actions" column that renders action buttons for each row.
 *
 * @typeParam BillingTableColumn - The type representing a column definition for the billing table.
 * @typeParam BillingTableRow - The type representing a row in the billing table.
 *
 * @see sharedTableColumns - Additional columns shared across tables.
 * @see ActionButtons - Component used to render action buttons in the "Actions" column.
 */
export const billingTableColumns: BillingTableColumn[] = [
  {
    header: 'Tenant name',
    accessorKey: 'tenantName',
    id: 'tenantName',
    cell: ({row}: CellContext<BillingTableRow, unknown>) => (
      <EllipsisText
        text={row.original.tenantName && row.original.tenantName.length > 0 ? row.original.tenantName : '-'}
      />
    ),
  },
  ...sharedTableColumns,
  {
    header: 'Actions',
    cell: (cellContext: CellContext<BillingTableRow, unknown>) => (
      <ActionButtons row={cellContext as CellContext<unknown, unknown>} />
    ),
  },
];

/**
 * Defines the columns for the invoice table, including shared columns and a custom "Actions" column.
 * The "Actions" column renders action buttons for each row using the `InvoiceActionButtons` component.
 *
 * @remarks
 * - The columns are based on the `BillingTableColumn` type.
 * - The "Actions" column uses a custom cell renderer to provide row-specific actions.
 *
 * @see sharedTableColumns
 * @see InvoiceActionButtons
 */
export const getInvoiceTableColumns = (tenantName: string, tenantId: string): BillingTableColumn[] => [
  ...sharedTableColumns,
  {
    header: 'Actions',
    cell: (cellContext: CellContext<BillingTableRow, unknown>) => (
      <InvoiceActionButtons
        row={cellContext as CellContext<unknown, unknown>}
        tenantName={tenantName}
        tenantId={tenantId}
      />
    ),
  },
];

/**
 * Renders a message indicating that the invoice page cannot be loaded due to missing tenant information.
 *
 * @param tenantName - The name of the tenant.
 * @param id - The unique identifier for the invoice or tenant.
 * @param billingCustomerId - The billing customer ID associated with the tenant.
 * @returns A React element displaying an error message if any of the parameters are missing; otherwise, returns nothing.
 */
export const canNotLoadInvoicePage = () => (
  <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: 300}}>
    <Typography variant="h6" color="text.secondary" sx={{mb: 2}}>
      Unable to load invoice page: Missing tenant information.
    </Typography>
  </Box>
);
