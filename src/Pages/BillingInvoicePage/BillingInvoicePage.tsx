import {Box, CircularProgress, Stack, Tooltip, Typography} from '@mui/material';
import EyeIcon from 'Assets/EyeIcon';
import {DebouncedInput, Table} from 'Components/Table';
import {useSnackbar} from 'notistack';
import React, {useEffect, useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';

import {CellContext} from '@tanstack/react-table';
import SearchIcon from 'Assets/search-icon.svg';
import FilterIcon from 'Assets/tenant-filter-icon.svg';
import SVGImageFromPath from 'Components/SVGImageFromPath';

import DownloadIcon from 'Assets/DownloadIcon';
import ViewAllInvoicesIcon from 'Assets/ViewAllInvoicesIcon';
import BorderButton from 'Components/BorderButton/BorderButton';
import Filter, {IFilter} from 'Components/Filter/Filter';
import {useTableState} from 'Components/Table/hook/TableStateHook';
import {Integers} from 'Helpers/integers';
import {
  useDownloadInvoiceByIdQuery,
  useGetTenantBillingsCountQuery,
  useGetTenantBillingsQuery,
} from 'redux/app/invoiceManagementApiSlice';
import {TenantBillingsApiDTO, TenantBillingsApiForCountDTO} from 'redux/app/types/invoice.type';
import {
  actionStyles,
  bodyCellProps,
  coloumnCellProps,
  headerBoxStyle,
  leftHeaderStyle,
  tableContainerProps,
  tableHeadProps,
  toolTipStyles,
} from 'styles/pages/TenantPage.styles';
import {renderFilterButton} from '../utils';
import {billingTableColumns, getBackendColumnName} from './billings.utils';
import FilterStatusChips from './BillingStatusFilterChip';

interface IActionButtonsProps {
  row: CellContext<unknown, unknown>;
}

/**
 * Renders action buttons for each invoice row, including:
 * - Viewing invoice details
 * - Downloading the invoice PDF
 * - Navigating to view all invoices for a tenant
 *
 * Handles download state, error notifications, and navigation with appropriate parameters.
 *
 * @param {IActionButtonsProps} props - The props for the component, including the invoice row data.
 * @returns {JSX.Element} The rendered action buttons.
 */
export const ActionButtons: React.FC<IActionButtonsProps> = ({row}) => {
  const navigate = useNavigate();
  const [downloadParams, setDownloadParams] = useState<{invoiceId: string; stripeInvoiceId: string} | null>(null);
  const {enqueueSnackbar} = useSnackbar();
  const [isDownloading, setIsDownloading] = useState(false);

  const tempParams = {invoiceId: '', stripeInvoiceId: ''};
  const {data, error, isLoading} = useDownloadInvoiceByIdQuery(downloadParams ?? tempParams, {skip: !downloadParams});

  useEffect(() => {
    if (data?.pdfUrl) {
      const link = document.createElement('a');
      link.href = data.pdfUrl;
      link.download = 'invoice.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setTimeout(() => {
        setIsDownloading(false);
        setDownloadParams(null);
      }, Integers.FourThousand);
    }
    if (error) {
      enqueueSnackbar('Failed to download invoice', {variant: 'error'});
      setDownloadParams(null);
      setIsDownloading(false);
    }
  }, [data, error]);

  const handleDownloadInvoice = (invoiceId: string, stripeInvoiceId: string) => {
    setDownloadParams({invoiceId, stripeInvoiceId});
    setIsDownloading(true);
  };

  const handleRedirectToDetails = (invoiceId: string) => {
    navigate(`/invoices/${invoiceId}`, {
      state: {
        tenantName: (row.row.original as {tenantName: string}).tenantName,
        invoiceId: (row.row.original as {id: string}).id,
        tenantId: (row.row.original as {tenantId: string}).tenantId,
        billingCustomerId: (row.row.original as {billingCustomerId: string}).billingCustomerId,
        fromOrigin: 'billing-invoices',
      },
    });
  };

  return (
    <Stack display="flex" flexDirection="row" gap={1}>
      <Tooltip title="View details" placement="top" arrow slotProps={toolTipStyles}>
        <EyeIcon sx={actionStyles} onClick={() => handleRedirectToDetails((row.row.original as {id: string}).id)} />
      </Tooltip>

      <Tooltip title="Download" placement="top" arrow slotProps={toolTipStyles}>
        {isLoading || isDownloading ? (
          <CircularProgress size={20} />
        ) : (
          <DownloadIcon
            sx={actionStyles}
            data-testid="download-icon"
            onClick={() =>
              handleDownloadInvoice(
                (row.row.original as {id: string}).id,
                (row.row.original as {invoiceId: string}).invoiceId,
              )
            }
          />
        )}
      </Tooltip>

      <Tooltip title="View all invoices" placement="top" arrow slotProps={toolTipStyles}>
        <ViewAllInvoicesIcon
          sx={actionStyles}
          data-testid="view-all-invoices-icon"
          onClick={() => {
            const original = row.row.original as {
              tenantId: string;
              tenantName?: string;
              id?: string;
              billingCustomerId?: string;
            };
            navigate(`/billing-invoices/${original.tenantId}`, {
              state: {
                tenantName: original.tenantName,
                id: original.id,
                billingCustomerId: original.billingCustomerId,
              },
            });
          }}
        />
      </Tooltip>
    </Stack>
  );
};

const buttonHeight = '2.375rem';

/**
 * BillingInvoice component displays a paginated, sortable, and filterable table of billing invoices.
 *
 * Features:
 * - Fetches and displays billing data and count from API endpoints.
 * - Supports searching, sorting, and filtering by status and date range.
 * - Handles pagination with manual control over limit and offset.
 * - Shows loading indicators and error notifications using notistack.
 * - Provides a top-right section with search and filter controls.
 * - Integrates with a custom Table component for displaying invoice data.
 *
 * State Management:
 * - Uses local state for search term, sorting, and selected filters.
 * - Uses custom hooks for table pagination state.
 *
 * Side Effects:
 * - Triggers initial sort on mount.
 * - Displays error notifications on API errors.
 *
 * @component
 */
const BillingInvoice: React.FC = () => {
  const {enqueueSnackbar} = useSnackbar();
  const {limit, setLimit, offset, setOffset, handlePageChange, handleRowsPerPageChange} = useTableState();
  const [searchTerm, setSearchTerm] = useState('');

  const [sortBy, setSortBy] = useState<string>('createdOn DESC');

  // Initial sort state for the table - use frontend column ID
  const initialSortState = [{id: 'createdDate', desc: true}];

  const [selectedIBillingFilter, setselectedIBillingFilter] = React.useState<IFilter | undefined>(undefined);
  const filterButtonRef = React.useRef<HTMLButtonElement>(null);
  const [openFilter, setOpenFilter] = React.useState(false);
  const location = useLocation();

  const handleSortChange = (columnId: string, sort: boolean) => {
    // Map frontend column name to backend column name
    const backendColumnName = getBackendColumnName(columnId);
    const sortParam = `${backendColumnName} ${sort ? 'DESC' : 'ASC'}`;
    setOffset(0);
    setSortBy(sortParam);
  };

  React.useEffect(() => {
    if (location?.state && 'statusFilter' in location.state) {
      const filter = location.state?.statusFilter;
      setselectedIBillingFilter({
        dateRange: undefined,
        status: filter,
      });
    }
  }, []);

  React.useEffect(() => {
    setOffset(0);
  }, [searchTerm]);

  // Build the filter object for the API call
  const filterParams: TenantBillingsApiDTO = {
    limit,
    offset,
    order: sortBy,
    searchValue: searchTerm,
    status: selectedIBillingFilter?.status ? Array.from(selectedIBillingFilter.status) : undefined,
    dateRange: selectedIBillingFilter?.dateRange,
  };

  // Build count filter (without limit/offset)
  const countFilterParams: TenantBillingsApiForCountDTO = {
    order: sortBy,
    searchValue: searchTerm,
    status: selectedIBillingFilter?.status ? Array.from(selectedIBillingFilter.status) : undefined,
    dateRange: selectedIBillingFilter?.dateRange,
  };

  const {
    data: billings,
    error: billingsError,
    isFetching: isLoading,
  } = useGetTenantBillingsQuery(filterParams, {
    refetchOnMountOrArgChange: true,
  });
  const {
    data: billingsCount,
    error: countError,
    isFetching: countLoading,
  } = useGetTenantBillingsCountQuery(countFilterParams, {
    refetchOnMountOrArgChange: true,
  });

  // Show error notifications
  React.useEffect(() => {
    if (billingsError) {
      enqueueSnackbar('Failed to fetch billings data', {variant: 'error'});
    }
    if (countError) {
      enqueueSnackbar('Failed to fetch billings count', {variant: 'error'});
    }
  }, [billingsError, countError, enqueueSnackbar]);
  // Trigger initial sort on mount
  React.useEffect(() => {
    handleSortChange('createdDate', true);
  }, []); // Empty dependency array means this runs once on mount

  // moved filter button to utils

  const buildTopRightSection = () => {
    return (
      <Box sx={{display: 'flex', gap: 1, flexDirection: 'row', alignItems: 'center', ml: 'auto'}}>
        {/* Search Input */}
        <DebouncedInput
          placeholder="Search invoices"
          data-testid="search-billings"
          sx={{
            fontSize: '0.675rem',
            pl: 0,
            width: '19rem',
          }}
          debounceTime={500}
          leftAdornment={<SVGImageFromPath path={SearchIcon} sx={{width: '1rem', height: '1rem', mr: 1}} />}
          inputSx={{
            fontSize: '1rem',
            fontWeight: 400,
            color: 'black.main',
          }}
          value={searchTerm}
          onChange={value => {
            setSearchTerm('' + value);
          }}
        />
        {renderFilterButton({
          filterButtonRef,
          buttonHeight,
          filterSize: (selectedIBillingFilter?.status?.size ?? 0) + (selectedIBillingFilter?.dateRange ? 1 : 0),
          hasFilters: !!((selectedIBillingFilter?.status?.size ?? 0) + (selectedIBillingFilter?.dateRange ? 1 : 0)),
          setOpenFilter,
          FilterIcon,
          BorderButton,
          SVGImageFromPath,
        })}
      </Box>
    );
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={headerBoxStyle}>
        <Typography variant="h6" sx={leftHeaderStyle}>
          Billing & Invoices
        </Typography>
        {buildTopRightSection()}
      </Box>
      <Box sx={{position: 'relative', minHeight: '200px'}}>
        <Table
          data={billings || []}
          columns={billingTableColumns}
          enableSorting={true}
          initialSortingState={initialSortState}
          tablePropsObject={{
            tableHeadProps: {sx: tableHeadProps},
            columnCellProps: {sx: coloumnCellProps},
            tableContainerProps: {sx: tableContainerProps},
            bodyCellProps: {sx: bodyCellProps},
          }}
          limit={limit}
          setLimit={setLimit}
          offset={offset}
          setOffset={setOffset}
          count={billingsCount?.count || 0}
          manualPagination={true}
          onSortChange={handleSortChange}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          data-testid="billings-table"
        />
        {(isLoading || countLoading) && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'rgba(255, 255, 255, 0.7)',
              zIndex: 1,
            }}
          >
            <CircularProgress />
          </Box>
        )}
      </Box>
      <Filter
        open={openFilter}
        value={selectedIBillingFilter}
        onClose={() => setOpenFilter(false)}
        anchorEl={filterButtonRef.current}
        onFilterChange={filter => {
          setOffset(0);
          setselectedIBillingFilter(filter);
        }}
        FilterStatusChips={FilterStatusChips}
      />
    </Box>
  );
};

export default BillingInvoice;
