import {Box, Breakpoint, Divider, Grid, Paper, Typography} from '@mui/material';
import DownloadIcon from 'Assets/DownloadIcon';
import BackdropLoader from 'Components/BackdropLoader';
import BorderButton from 'Components/BorderButton/BorderButton';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import EllipsisText from 'Components/EllipsisText/EllipsisText';
import StatusChip from 'Components/StatusChip/StatusChip';
import {defaultDateFormat} from 'Constants/enums';
import {Integers} from 'Helpers/integers';
import {useSnackbar} from 'notistack';
import React, {useEffect} from 'react';
import {useLocation, useNavigate} from 'react-router';
import {useDownloadInvoiceByIdQuery, useGetInvoiceByIdQuery} from 'redux/app/invoiceManagementApiSlice';
import {RouteNames} from 'Routes/routeNames';
import {
  canNotLoadInvoicePage,
  getFontColor,
  getIndicatorColor,
  getStatusColor,
  getStatusLabel,
  InvoiceStatus,
} from './billings.utils';

const PX = 1.5;

export const titleLabelSx = {
  fontWeight: 600,
  fontSize: '0.75rem',
  color: 'body.500',
};

/**
 * Styles for the subtitle label in the user detail view.
 */
export const subtitleLabelSx = {
  fontWeight: 600,
  fontSize: '0.875rem',
  color: 'body.800',
  textTransform: 'capitalize',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  maxWidth: '100%',
};

/**
 * Checks if any of the required keys for invoice details are missing.
 *
 * @param tenantName - The name of the tenant.
 * @param id - The unique identifier for the invoice.
 * @param tenantId - The unique identifier for the tenant.
 * @param billingCustomerId - The unique identifier for the billing customer.
 * @returns `true` if any of the parameters are missing or falsy, otherwise `false`.
 */
const isKeysMissing = (tenantName: string, id: string, tenantId: string, billingCustomerId: string) =>
  !tenantName || !id || !billingCustomerId || !tenantId;

const gridSize: {[key in Breakpoint]?: number | null} = {xs: 12, sm: 4, md: 3};

/**
 * Displays detailed information about a specific invoice, including invoice metadata,
 * payment details, and navigation breadcrumbs. Handles loading and error states,
 * and provides actions such as downloading the invoice and navigating back to the invoice list.
 *
 * @returns {JSX.Element} The rendered invoice detail page.
 *
 * @remarks
 * - Expects navigation state to provide `tenantName`, `invoiceId`, `tenantId`, `billingCustomerId`, and `fromOrigin`.
 * - Fetches invoice data using `useGetInvoiceByIdQuery`.
 * - Shows a loader while fetching data and displays an error notification if fetching fails.
 * - Renders invoice and payment details, breadcrumbs, and action buttons.
 */
export default function InvoiceDetail() {
  const location = useLocation();
  const {tenantName, invoiceId, tenantId, billingCustomerId, fromOrigin} = location.state || {};
  const {enqueueSnackbar} = useSnackbar();

  const skipApi = isKeysMissing(tenantName, invoiceId, tenantId, billingCustomerId);
  const [downloadParams, setDownloadParams] = React.useState<{invoiceId: string; stripeInvoiceId: string} | null>(null);
  const [isDownloading, setIsDownloading] = React.useState(false);

  const {
    data: invoice,
    isLoading: isInvoiceLoading,
    error: invoiceError,
  } = useGetInvoiceByIdQuery(invoiceId, {
    skip: skipApi,
  }) || {};

  const tempParams = {invoiceId: '', stripeInvoiceId: ''};
  const {data, error, isLoading} = useDownloadInvoiceByIdQuery(downloadParams ?? tempParams, {skip: !downloadParams});

  const navigate = useNavigate();
  const secondaryDark = 'body.dark';

  React.useEffect(() => {
    if (invoiceError) {
      enqueueSnackbar('Failed to fetch invoice data', {variant: 'error'});
    }
  }, [invoiceError, enqueueSnackbar]);

  useEffect(() => {
    if (data?.pdfUrl) {
      const link = document.createElement('a');
      link.href = data.pdfUrl;
      link.download = 'invoice.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setTimeout(() => {
        setIsDownloading(false);
        setDownloadParams(null);
      }, Integers.FourThousand);
    }
    if (error) {
      enqueueSnackbar('Failed to download invoice', {variant: 'error'});
      setDownloadParams(null);
      setIsDownloading(false);
    }
  }, [data, error]);

  const handleDownloadInvoice = (invoiceId: string, stripeInvoiceId: string) => {
    setDownloadParams({invoiceId, stripeInvoiceId});
    setIsDownloading(true);
  };

  const breadcrumbs = () => {
    if (fromOrigin === 'billing-invoices') {
      return [
        {
          label: 'Billings & Invoices',
          url: RouteNames.BILLING_INVOICES,
          state: {
            tenantName: tenantName,
            id: tenantId,
            billingCustomerId: billingCustomerId,
          },
        },
        {
          label: `${tenantName}`,
          url: `${RouteNames.BILLING_INVOICES}`,
        },
      ];
    }

    return [
      {
        label: 'Billings & Invoices',
        url: RouteNames.BILLING_INVOICES,
        state: {
          tenantName: tenantName,
          id: tenantId,
          billingCustomerId: billingCustomerId,
        },
      },
      {
        label: 'All Invoices',
        url: `${RouteNames.BILLING_INVOICES}/${tenantId}`,
        state: {
          tenantName: tenantName,
          id: tenantId,
          billingCustomerId: billingCustomerId,
        },
      },
      {
        label: `${invoice.invoiceId}`,
        url: `RouteNames.INVOICE_DETAILS/${invoiceId}`,
        state: {
          tenantName: tenantName,
          id: tenantId,
          billingCustomerId: billingCustomerId,
        },
      },
    ];
  };

  const buildBottomSection = () => {
    return (
      <Box sx={{display: 'flex', flexDirection: 'column', mt: 'auto', mb: 1.5}} data-testid="bottom-section">
        <Divider sx={{my: 2}} />
        <Box sx={{alignSelf: 'flex-end'}} px={PX}>
          <BorderButton
            sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
            fullWidth={false}
            onClick={() => navigate(-1)}
          >
            Close
          </BorderButton>
        </Box>
      </Box>
    );
  };

  const buildTopPart = () => {
    const status = invoice?.invoiceStatus as InvoiceStatus;
    const backgroundColor = getStatusColor(status);
    const color = getFontColor(status);
    const indicatorColor = getIndicatorColor(status);
    const label = getStatusLabel(status);
    return (
      <Box display="flex" alignItems="center" gap={1} data-testid="top-part">
        <Box display={'flex'} flexDirection={'column'} gap={1}>
          <Box display={'flex'} flexDirection={'row'} alignItems="center" gap={1}>
            <Typography sx={{fontSize: '1.25rem', fontWeight: 700}}>{tenantName}</Typography>
            <StatusChip label={label} backgroundColor={backgroundColor} indicatorColor={indicatorColor} color={color} />
          </Box>
          <Box sx={{display: 'flex', alignItems: 'center'}}>
            <Box
              sx={{
                height: 12,
                borderColor: 'body.100',
              }}
            />
            <Breadcrumb items={breadcrumbs()} separator="|" />
          </Box>
        </Box>
        <Box sx={{ml: 'auto', display: 'flex', flexDirection: 'row', gap: 1, alignItems: 'center'}}>
          <Typography
            data-testid="view-all-invoices"
            sx={{
              fontWeight: 600,
              color: 'secondary.main',
              fontSize: '0.8125rem',
              pl: 2,
              cursor: 'pointer',
              textDecoration: 'underline',
              '&:hover': {textDecoration: 'underline', opacity: 0.8},
            }}
            onClick={() => {
              navigate(`/billing-invoices/${tenantId}`, {
                state: {
                  tenantName: tenantName,
                  id: tenantId,
                  billingCustomerId: billingCustomerId,
                },
              });
            }}
            tabIndex={0}
          >
            View all invoices
          </Typography>
          <BorderButton
            data-testid="download-invoice-button"
            loading={isDownloading || isLoading}
            sx={{fontWeight: 700, px: '1.56rem'}}
            onClick={() => handleDownloadInvoice(invoiceId, invoice?.invoiceId)}
          >
            <Box sx={{display: 'flex', alignItems: 'center', gap: 0.5}} data-testid="download-invoice">
              <DownloadIcon sx={{fill: 'white', width: '1.2rem'}} data-testid="download-icon" />
              Download Invoice
            </Box>
          </BorderButton>
        </Box>
      </Box>
    );
  };

  if (isInvoiceLoading) {
    return <BackdropLoader />;
  }

  if (skipApi) {
    return canNotLoadInvoicePage();
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 0.5,
      }}
    >
      {buildTopPart()}
      {/* Header */}

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1.5,
        }}
      >
        <Box
          sx={{
            border: '1px solid',
            borderColor: 'body.200',
            borderRadius: '0.375rem',
            pt: 1,
            minHeight: '80vh',
            display: 'flex',
            flexDirection: 'column',
          }}
          data-testid="invoice-detail"
        >
          <Typography sx={{fontSize: '1rem', fontWeight: 700, color: 'body.900'}} px={PX}>
            Invoicing information
          </Typography>

          <Grid container spacing={2} px={PX} mt={1}>
            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>Invoice no.</Typography>
              <EllipsisText
                text={invoice?.invoiceNumber ?? invoice?.invoiceId ?? '-'}
                sx={{...subtitleLabelSx, fontWeight: 700}}
              />
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>Invoice Date</Typography>
              <Typography sx={subtitleLabelSx}>
                {invoice?.createdOn ? defaultDateFormat(invoice?.createdOn) : '-'}
              </Typography>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>Due Date</Typography>
              <Typography sx={subtitleLabelSx}>
                {invoice?.dueDate ? defaultDateFormat(invoice?.dueDate) : '-'}
              </Typography>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>From</Typography>
              <Typography sx={subtitleLabelSx}>
                {invoice?.startDate ? defaultDateFormat(invoice?.startDate) : '-'}
              </Typography>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>To</Typography>
              <Typography sx={subtitleLabelSx}>
                {invoice?.endDate ? defaultDateFormat(invoice?.endDate) : '-'}
              </Typography>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>Invoice amount</Typography>
              <Typography sx={subtitleLabelSx}>${invoice?.amount ?? '-'}</Typography>
            </Grid>
          </Grid>

          <Typography sx={{fontSize: '1rem', fontWeight: 700, color: 'body.900', mt: 2}} px={PX}>
            Payment details
          </Typography>

          <Paper
            variant="outlined"
            data-testid="payment-details"
            sx={{p: 2, m: 1.5, borderRadius: 1, borderColor: 'secondary.100', backgroundColor: 'secondary.25'}}
          >
            <Box sx={{display: 'flex', alignItems: 'center', py: 1}}>
              <Typography
                variant="body1"
                sx={{color: 'body.600', fontWeight: 600, fontSize: '0.875rem', lineHeight: '1.125rem'}}
              >
                Payment date
              </Typography>
              <Box sx={{flex: 1, borderBottom: '0.125rem dotted', borderColor: 'secondary.200', mx: 2}} />
              <Typography
                variant="body1"
                data-testid="plan-cost"
                sx={{fontWeight: 700, fontSize: '0.875rem', lineHeight: '1.125rem', color: secondaryDark}}
              >
                {defaultDateFormat(invoice?.createdOn)}
              </Typography>
            </Box>
            <Box sx={{display: 'flex', alignItems: 'center', py: 1}}>
              <Typography
                variant="body1"
                sx={{color: 'body.600', fontWeight: 600, fontSize: '0.875rem', lineHeight: '1.125rem'}}
              >
                Amount
              </Typography>
              <Box sx={{flex: 1, borderBottom: '0.125rem dotted', borderColor: 'secondary.200', mx: 2}} />
              <Typography
                data-testid="users-cost"
                variant="body1"
                sx={{fontWeight: 700, fontSize: '0.875rem', lineHeight: '1.125rem', color: secondaryDark}}
              >
                ${invoice?.amount}
              </Typography>
            </Box>
            <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 1.5}}>
              <Typography
                variant="subtitle1"
                sx={{fontWeight: 700, fontSize: '0.875rem', lineHeight: '1.125rem', color: secondaryDark}}
              >
                Total
              </Typography>
              <Typography
                data-testid="total-cost"
                variant="h6"
                sx={{fontWeight: 700, fontSize: '1.375rem', lineHeight: '1.875rem', color: secondaryDark}}
              >
                ${invoice?.amount}
              </Typography>
            </Box>
          </Paper>

          {buildBottomSection()}
        </Box>
      </Box>
    </Box>
  );
}
