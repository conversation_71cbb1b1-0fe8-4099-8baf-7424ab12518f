import {useCallback, useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {PlanResponse} from 'redux/app/types/plan.type';
import {RouteNames} from 'Routes/routeNames';
import {IFile} from 'types';
import {FormAddTenant, TenantCreationStepType} from '../addTenantsUtils';

export const useAddTenantState = (initialValues?: FormAddTenant) => {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState<TenantCreationStepType>(0);
  const [nextButtonState, setNextButtonState] = useState(false);
  const [trialSelected, setTrialSelected] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const [createdTenant, setCreatedTenant] = useState<string | undefined>(undefined);
  const [files, setFiles] = useState<File[]>([]);
  const [existingFiles, setExistingFiles] = useState<IFile[]>([]);
  useEffect(() => {
    setExistingFiles(initialValues?.files || []);
  }, [initialValues?.files]);

  const [overAllPlan, setOverAllPlan] = useState<PlanResponse | null>(null);
  const [userCount, setUserCount] = useState<number>(1);
  const [selectedDevice, setSelectedDevice] = useState<string | undefined>(undefined);
  const [selectedTenure, setSelectedTenure] = useState<string | undefined>(undefined);
  const [selectedInfraConfig, setSelectedInfraConfig] = useState<string | undefined>(undefined);

  const handleNext = useCallback(() => {
    setActiveStep(current => Math.min(current + 1, 2) as TenantCreationStepType);

    setNextButtonState(false);
  }, []);

  const handleBack = useCallback(
    () =>
      setActiveStep(current => {
        if (current === TenantCreationStepType.PlanDetails) {
          setNextButtonState(false);
        }
        if (current === TenantCreationStepType.Documents && overAllPlan) {
          setNextButtonState(false);
        }
        return Math.max(0, current - 1) as TenantCreationStepType;
      }),
    [overAllPlan],
  );

  const handleNextButton = useCallback((state: boolean) => {
    setNextButtonState(state);
  }, []);

  const onNavigateToTenant = () => {
    setActiveStep(TenantCreationStepType.TenantDetails);
    setFiles([]);
    setOverAllPlan(null);
    setUserCount(1);
    setSelectedDevice(undefined);
    setSelectedTenure(undefined);
    setSelectedInfraConfig(undefined);
    setIsDialogOpen(false);
    setCreatedTenant(undefined);
    navigate(RouteNames.ADD_TENANT);
  };

  return {
    activeStep,
    handleNext,
    handleBack,
    handleNextButton,
    files,
    overAllPlan,
    userCount,
    selectedDevice,
    selectedTenure,
    selectedInfraConfig,
    isDialogOpen,
    setFiles,
    setOverAllPlan,
    setUserCount,
    setSelectedDevice,
    setSelectedTenure,
    setSelectedInfraConfig,
    setIsDialogOpen,
    onNavigateToTenant,
    nextButtonState,
    setActiveStep,
    createdTenant,
    setCreatedTenant,
    existingFiles,
    setExistingFiles,
    trialSelected,
    setTrialSelected,
  };
};
