/**
 * This component renders the tenant detail form used for onboarding or editing a tenant.
 * It includes validation for subdomain availability and suggestions if the subdomain is taken.
 *
 * Features:
 * - Debounced subdomain availability check
 * - Dynamic adornment icons
 * - Read-only toggle for edit mode
 * - Company and contact detail input fields
 */

import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import {
  Box,
  CircularProgress,
  Grid,
  InputAdornment,
  List,
  ListItem,
  ListItemButton,
  MenuItem,
  Select,
  Typography,
} from '@mui/material';
import FormInput from 'Components/Forms/FormInput';
import FormSelect from 'Components/Forms/FormSelect/FormSelect';
import {REGISTERED_DOMAIN} from 'Constants/enums';
import {useFormikContext} from 'formik';
import {Integers} from 'Helpers/integers';
import {StyleUtils} from 'Helpers/styleUtils';
import {debounce} from 'lodash';
import NoItemRenderer from 'Pages/PendingTenants/NoItemRenderer';
import {forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState} from 'react';
import {GetCity, GetState} from 'react-country-state-city';
import {useLazyValidateUserQuery, useVerifyTenantKeyMutation} from 'redux/app/tenantManagementApiSlice';
import {AnyObject} from 'yup';
import Icon from '../../../Assets/Icons.svg';
import {TenantStatus} from '../tenants.utils';
import {addTenantValidationSchema, countryCodes, FormAddTenant, steps} from './addTenantsUtils';
import {UserValidityDialog} from './UserValidityDialog';

const alertSuccessMain = 'alert.success.onBg';
/**
 * API response type for verifying tenant key availability
 */
export type VerifyTenantKeyResponse = {
  available: boolean;
  suggestions?: string[];
};
const DEBOUNCE_DELAY_MS = 500;
interface Props {
  /** When true, disables editing of fields */
  isEdit?: boolean;
  tenantStatus?: number;
  triggerValidateUser?: any;
  validateUserLoading?: boolean;
}
// Error type for validateUserCombo catch block
interface ValidateUserError {
  status?: number;
  data?: {
    error?: {
      details?: {
        username?: string;
        email?: string;
      };
    };
  };
}

/**
 * Returns an adornment component (spinner/icon/cancel) for subdomain input
 */
const getEndAdornmentContent = ({
  key,
  loading,
  isAvailable,
  setFieldValue,
  setIsAvailable,
  setSuggestions,
  isKeyFieldValid,
  isEdit = false,
}: {
  key: string;
  loading: boolean;
  isAvailable: boolean | null;
  setFieldValue: (field: string, value: unknown, shouldValidate?: boolean) => void;
  setIsAvailable: (val: boolean | null) => void;
  setSuggestions: (val: string[]) => void;
  isKeyFieldValid?: boolean;
  isEdit?: boolean;
}) => {
  if (isEdit) {
    // If in edit mode, we might want to show different validation states
    return <img src={Icon} alt="Available" style={{width: 20, height: 20, objectFit: 'contain'}} />;
  }
  if ((key || '').length < steps.length) return null;

  if (loading && isKeyFieldValid) {
    return <CircularProgress size={20} />;
  }

  if (isAvailable === true && isKeyFieldValid) {
    return <img src={Icon} alt="Available" style={{width: 20, height: 20, objectFit: 'contain'}} />;
  }

  if (isAvailable === false && isKeyFieldValid) {
    return (
      <CancelOutlinedIcon
        color="error"
        sx={{cursor: 'pointer'}}
        onClick={() => {
          setFieldValue('key', '');
          setIsAvailable(null);
          setSuggestions([]);
        }}
      />
    );
  }

  return null;
};

type CustomNoItemRendererProps = {
  stateSelected: boolean;
};

export const CustomNoItemRenderer: React.FC<CustomNoItemRendererProps> = ({stateSelected}) => (
  <NoItemRenderer stateSelected={stateSelected} />
);

const renderNoItem = (stateSelected: boolean) => () => <CustomNoItemRenderer stateSelected={stateSelected} />;

/**
 * Returns MUI styles for the subdomain input box based on availability
 */
const getSxPropsValue = (isAvailable: boolean | null, keyLength: number, isKeyFieldValid?: boolean): AnyObject => {
  if ((isAvailable === true && keyLength < steps.length) || !isKeyFieldValid) {
    return {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: 'body.100',
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: 'body.100',
      },
    };
  }

  if (isAvailable === true) {
    return {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: alertSuccessMain,
      },
      '&:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: alertSuccessMain,
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: alertSuccessMain,
      },
    };
  }

  if (isAvailable === false) {
    return {
      '&:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: 'alert.error.onBg',
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: 'alert.error.onBg',
      },
    };
  }

  return {};
};

type Mode = 'edit' | 'view' | 'create';

function getStyles(mode: Mode) {
  const styles: Record<Mode, object> = {
    edit: {
      backgroundColor: '#E9E9F1',
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: '#C0CEEA',
      },
      color: '#607294',
    },
    view: {},
    create: {},
  };

  return styles[mode];
}

/**
 * A form component for entering tenant company and contact information.
 * Supports both add and edit modes.
 */

const AddTenantDetail = forwardRef<{validateUserCombo: () => Promise<boolean>}, Props>(
  ({isEdit, tenantStatus, triggerValidateUser, validateUserLoading}, ref) => {
    const {values, errors, setFieldValue, validateForm, touched, setFieldError, setFieldTouched, setValues} =
      useFormikContext<FormAddTenant>();
    const [openDialog, setOpenDialog] = useState(false);
    const [userComboError, setUserComboError] = useState<string | null>(null);

    // Sync userComboError to Formik values for cross-component access
    useEffect(() => {
      setFieldValue('userComboError', userComboError, false);
    }, [userComboError, setFieldValue]);
    const editModeStyles = isEdit ? getStyles('edit') : {};
    const [cityOptions, setCityOptions] = useState<{value: string; label: string}[]>([]);
    const [stateOptions, setStateOptions] = useState<{value: string; label: string}[]>([]);

    const [verifyTenantKey] = useVerifyTenantKeyMutation();
    const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
    const [suggestions, setSuggestions] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);
    // Use triggerValidateUser and validateUserLoading from props if provided
    const triggerValidateUserProp = typeof triggerValidateUser !== 'undefined' ? triggerValidateUser : undefined;
    const validateUserLoadingProp = typeof validateUserLoading !== 'undefined' ? validateUserLoading : false;
    // Keep local hook for fallback, but prefer props
    const [
      triggerValidateUserLocal,
      {data: validateUserData, error: validateUserError, isFetching: validateUserLoadingLocal},
    ] = useLazyValidateUserQuery();
    const triggerValidateUserFinal = triggerValidateUserProp || triggerValidateUserLocal;
    const validateUserLoadingFinal = validateUserLoadingProp || validateUserLoadingLocal;

    const shouldDisable = tenantStatus !== TenantStatus.ACTIVE && isEdit;
    /**
     * Debounced API call to verify tenant key availability
     */
    const debouncedVerify = useCallback(
      debounce(async (key: string, companyName: string) => {
        if (!key.trim()) return;
        setLoading(true);
        try {
          const res: VerifyTenantKeyResponse = await verifyTenantKey({key, companyName}).unwrap();
          setIsAvailable(res.available);
          setSuggestions(res.available ? [] : res.suggestions || []);
        } catch {
          setIsAvailable(false);
        } finally {
          setLoading(false);
        }
      }, DEBOUNCE_DELAY_MS),
      [],
    );

    const handleDialogClose = () => {
      setOpenDialog(false);
      if (dialogResolveRef.current) {
        dialogResolveRef.current(false);
        dialogResolveRef.current = null;
      }
    };

    const dialogResolveRef = useRef<((result: boolean) => void) | null>(null);

    const handleValidityDialog = () => {
      setOpenDialog(false);
      if (dialogResolveRef.current) {
        dialogResolveRef.current(true);
        dialogResolveRef.current = null;
      }
    };

    // Expose validateUserCombo method to parent via ref
    useImperativeHandle(ref, () => ({
      async validateUserCombo() {
        if (!isEdit) return true;
        if (shouldDisable) return true;
        try {
          const res = await triggerValidateUserFinal({email: values.email, userName: values.userName}).unwrap();
          if (res?.message === 'User already exists' && res?.data) {
            setUserComboError(null);
            setValues(prev => ({
              ...prev,
              userName: res.data.username ?? prev.userName,
              email: res.data.email ?? prev.email,
            }));
            return true;
          } else {
            setUserComboError(null);
            return true;
          }
        } catch (error: unknown) {
          const err = error as ValidateUserError;
          const errorStatusCode = err?.status;
          const userDetails = err.data?.error?.details;
          if (errorStatusCode === Integers.FourHundredTen) {
            setFieldError('userName', 'Username is already taken');
            setFieldValue('isUserNameValid', false, false);
          }
          if (userDetails) {
            setValues(prev => ({
              ...prev,
              userName: userDetails.username ?? prev.userName,
              email: userDetails.email ?? prev.email,
            }));
            setOpenDialog(true);
            // Promise-based dialog confirmation (type-safe)
            return new Promise<boolean>(resolve => {
              dialogResolveRef.current = resolve;
            });
          }
          return false;
        }
      },
    }));

    /**
     * Trigger subdomain check when value changes and not in edit mode
     */
    useEffect(() => {
      if (values.key && !isEdit) {
        // Validate subdomain with Yup before calling debouncedVerify
        addTenantValidationSchema
          .validateAt('key', {key: values.key})
          .then(() => {
            debouncedVerify(values.key, values.company);
          })
          .catch(() => {
            // Do not call debouncedVerify if validation fails
          });
      }
    }, [values.key, debouncedVerify, isEdit]);

    const USA_ID = 233;

    useEffect(() => {
      const loadStates = async () => {
        try {
          const states = await GetState(USA_ID);
          setStateOptions(
            (states || []).map(state => ({
              value: state.id?.toString() || state.name,
              label: state.name,
            })),
          );
        } catch {
          setStateOptions([]);
        }
      };
      loadStates();
    }, []);
    useEffect(() => {
      if (!values.state) {
        setCityOptions([]);
        setFieldValue('city', '');
        return;
      }
      GetCity(USA_ID, Number(values.state)).then(cities => {
        const match = (cities || []).find(city => city.id?.toString() === values.city);

        setCityOptions(
          (cities || []).map(city => ({
            value: city.id?.toString() || city.name,
            label: city.name,
          })),
        );
        setFieldValue('city', match?.id?.toString() ?? '');
      });
    }, [values.state]);

    // To check if the key field has an error AND has been touched
    const isKeyFieldValid = !errors.key;
    const sxPropsValue = getSxPropsValue(isAvailable, (values.key || '').length, isKeyFieldValid);
    const adornmentContent = getEndAdornmentContent({
      key: values.key,
      loading,
      isAvailable,
      setFieldValue,
      setIsAvailable,
      setSuggestions,
      isKeyFieldValid,
      isEdit,
    });

    // Validate form when city changes
    useEffect(() => {
      validateForm();
    }, [values.city]);

    return (
      <Grid container spacing={2} rowSpacing={2} sx={{height: '100%', boxSizing: 'border-box', padding: 2}}>
        {/* Section Header */}
        <Grid size={{xs: 12}}>
          <Typography variant="h6" fontWeight={600} sx={{fontSize: '1rem'}}>
            Tenant Information
          </Typography>
        </Grid>

        {/* Company Name */}
        <Grid size={{xs: 12, sm: 6}}>
          <Typography sx={StyleUtils.lalelStyles}>Company *</Typography>
          <FormInput
            fullWidth
            id="company"
            name="company"
            required
            sx={{...StyleUtils.inputBoxStyles, ...editModeStyles}}
            readOnly={isEdit}
            placeholder="Enter company name"
          />
        </Grid>

        {/* Subdomain Key */}
        <Grid container spacing={0} size={{xs: 12, sm: 6}} alignItems="stretch">
          <Grid size={{xs: 9}}>
            <Typography sx={StyleUtils.lalelStyles}>Subdomain *</Typography>
            <Box sx={{position: 'relative', display: 'inline-block', width: '100%'}}>
              <FormInput
                fullWidth
                id="key"
                name="key"
                required
                sx={[{...StyleUtils.inputBoxStyles, ...editModeStyles}, sxPropsValue]}
                placeholder="Enter subdomain"
                readOnly={isEdit}
                endAdornment={
                  adornmentContent && (
                    <InputAdornment position="end" sx={{pr: 1}}>
                      {adornmentContent}
                    </InputAdornment>
                  )
                }
              />

              {/* Subdomain suggestions */}
              {isAvailable === false && (
                <Box
                  sx={{
                    border: theme => `0.0625rem solid ${theme.palette.body[Integers.OneHundred]}`,
                    mt: 1,
                    borderRadius: 1,
                    backgroundColor: 'white.main',
                    position: 'absolute',
                    zIndex: 2,
                    width: '100%',
                  }}
                >
                  <Typography sx={{fontSize: '0.85rem', p: 1}}>
                    <Typography component="span" sx={{color: 'alert.error.main', fontSize: '0.85rem'}}>
                      <strong>&quot;{values.key}&quot; is already taken.</strong>
                    </Typography>
                    <br />
                    Here are some suggestions.
                  </Typography>

                  {suggestions.length > 0 && (
                    <Grid container justifyContent="center" alignItems="center">
                      {suggestions.map(suggestion => (
                        <Grid size={{xs: 12}} key={suggestion} sx={{display: 'flex', justifyContent: 'center'}}>
                          <List dense sx={{width: '100%', display: 'flex', justifyContent: 'center', p: 0, m: 0}}>
                            <ListItem
                              disablePadding
                              sx={{
                                backgroundColor: 'white.200',
                                borderRadius: 2,
                                width: '96%',
                                mb: 1,
                              }}
                            >
                              <ListItemButton
                                data-testid={`suggestion-${suggestion}`}
                                onClick={() => setFieldValue('key', suggestion)}
                                sx={{fontSize: '0.9rem'}}
                              >
                                {suggestion + REGISTERED_DOMAIN}
                              </ListItemButton>
                            </ListItem>
                          </List>
                        </Grid>
                      ))}
                    </Grid>
                  )}
                </Box>
              )}
            </Box>
          </Grid>

          {/* Domain suffix */}
          <Grid size={{xs: 3}}>
            <Box sx={{height: '100%', display: 'flex', paddingTop: '1.2rem', alignItems: 'center', pl: 0.5}}>
              <Typography>{REGISTERED_DOMAIN}</Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Contact Info Header */}
        <Grid size={{xs: 12}} sx={{mt: 2}}>
          <Typography variant="h6" fontWeight={600} sx={{fontSize: '1rem'}}>
            Contact Information
          </Typography>
        </Grid>

        {/* Contact Fields */}
        <Grid size={{xs: 12, sm: 6}}>
          <Typography sx={StyleUtils.lalelStyles}>First name *</Typography>
          <FormInput
            fullWidth
            id="firstName"
            name="firstName"
            placeholder="Enter first name"
            required
            sx={{...StyleUtils.inputBoxStyles}}
          />
        </Grid>
        <Grid size={{xs: 12, sm: 6}}>
          <Typography sx={StyleUtils.lalelStyles}>Last name *</Typography>
          <FormInput
            fullWidth
            id="lastName"
            name="lastName"
            placeholder="Enter last name"
            required
            sx={{...StyleUtils.inputBoxStyles}}
          />
        </Grid>
        <Grid size={{xs: 12, sm: 6}}>
          <Typography sx={StyleUtils.lalelStyles}>User name *</Typography>
          <FormInput
            fullWidth
            id="userName"
            name="userName"
            placeholder="Enter username"
            required
            readOnly={shouldDisable}
            sx={{
              ...StyleUtils.inputBoxStyles,
              ...(shouldDisable ? editModeStyles : {}),
            }}
          />
        </Grid>
        <Grid size={{xs: 12, sm: 6}}>
          <Typography sx={StyleUtils.lalelStyles}>Email address *</Typography>
          <FormInput
            fullWidth
            id="email"
            name="email"
            type="email"
            placeholder="Enter email address"
            required
            readOnly={shouldDisable}
            sx={{
              ...StyleUtils.inputBoxStyles,
              ...(shouldDisable ? editModeStyles : {}),
            }}
          />
        </Grid>
        <Grid size={{xs: 12, sm: 6}}>
          <Typography sx={StyleUtils.lalelStyles}>Job title</Typography>
          <FormInput
            fullWidth
            id="designation"
            name="designation"
            placeholder="Enter job title"
            sx={{...StyleUtils.inputBoxStyles}}
          />
        </Grid>
        <Grid size={{xs: 12, sm: 6}}>
          <Typography sx={StyleUtils.lalelStyles}>Primary phone number</Typography>
          <FormInput
            fullWidth
            id="mobileNumber"
            name="mobileNumber"
            placeholder="Enter primary phone number"
            onInput={e => {
              const target = e.target as HTMLInputElement;
              target.value = target.value.replace(/\D/g, '');
            }}
            sxProps={{paddingLeft: 0}}
            sx={{...{...StyleUtils.inputBoxStyles}, p: 0}}
            startAdornment={
              <InputAdornment position="start" sx={{...StyleUtils.inputAdornment}}>
                <Select
                  data-testid="country-code-select"
                  variant="standard"
                  disableUnderline
                  disabled
                  defaultValue={values?.countryCode?.code || '+1'}
                  sx={{
                    ...StyleUtils.selectBox,
                    '& .Mui-disabled': {
                      '-webkit-text-fill-color': 'black.main',
                    },
                  }}
                  IconComponent={() => null}
                >
                  {countryCodes.map(option => (
                    <MenuItem key={option.code} value={option.code} data-testid={`country-code-${option.code}`}>
                      {option.code}
                    </MenuItem>
                  ))}
                </Select>
              </InputAdornment>
            }
          />
        </Grid>

        <Grid size={{xs: 12, sm: 6}}>
          <Typography sx={StyleUtils.lalelStyles}>State *</Typography>
          <FormSelect
            fullWidth
            id="state"
            name="state"
            placeholder="Select state"
            required
            sx={{...StyleUtils.selectBoxStyles}}
            options={stateOptions}
            menuPlacement="top"
            placeholderSx={{color: 'body.300'}}
            onChange={value => {
              const selected = stateOptions.find(opt => opt.value === value);

              setFieldValue('state', value);
              setFieldValue('stateId', selected ? selected.label : '');
            }}
          />
        </Grid>
        <Grid size={{xs: 12, sm: 6}}>
          <Typography sx={StyleUtils.lalelStyles}>City *</Typography>
          <FormSelect
            fullWidth
            id="city"
            name="city"
            placeholder="Select city"
            required
            sx={{...StyleUtils.selectBoxStyles}}
            options={cityOptions}
            disabled={!values.state}
            menuPlacement="top"
            placeholderSx={{color: 'body.300'}}
            onChange={value => {
              const selected = cityOptions.find(opt => opt.value === value);
              setFieldValue('city', value);
              setFieldValue('cityId', selected ? selected.label : '');
            }}
            noItemRenderer={renderNoItem(!!values.state)}
          />
        </Grid>
        {openDialog && (
          <UserValidityDialog
            onConfirm={() => handleValidityDialog()}
            actionType={'convert'}
            open={openDialog}
            onClose={handleDialogClose}
            title={values.userName + ' - ' + values.email}
          />
        )}
      </Grid>
    );
  },
);

export default AddTenantDetail;
