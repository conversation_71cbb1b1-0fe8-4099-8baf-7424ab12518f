import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {Formik} from 'formik';
import {Provider} from 'react-redux';
import {store} from 'redux/store';
import AddTenantDetail from './AddTenantDetail';
import {initialAddTenantValues} from './addTenantsUtils';

// Mock API hooks
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useLazyValidateUserQuery: () => [
    vi.fn().mockImplementation(params => {
      if (params.email === '<EMAIL>') {
        return Promise.reject('Email exists');
      }
      return Promise.resolve();
    }),
    {data: null, error: null, isFetching: false},
  ],
  useVerifyTenantKeyMutation: () => [vi.fn()],
}));

// Mock react-country-state-city
vi.mock('react-country-state-city', () => ({
  GetState: () => Promise.resolve([]),
  GetCity: () => Promise.resolve([]),
}));

const renderComponent = () => {
  return render(
    <Provider store={store}>
      <Formik initialValues={initialAddTenantValues()} onSubmit={vi.fn()}>
        <AddTenantDetail />
      </Formik>
    </Provider>,
  );
};

describe('AddTenantDetail', () => {
  it('should show loading state while validating email', async () => {
    renderComponent();
    const emailInput = screen.getByPlaceholderText('Enter email address');

    // Type valid email
    await userEvent.type(emailInput, '<EMAIL>');

    // Should show loading state
    expect(screen.getByText('Validating email...')).toBeInTheDocument();

    // Wait for validation to complete
    await waitFor(() => {
      expect(screen.queryByText('Validating email...')).not.toBeInTheDocument();
    });
  });

  it('should show error for existing email', async () => {
    renderComponent();
    const emailInput = screen.getByPlaceholderText('Enter email address');

    // Type existing email
    await userEvent.type(emailInput, '<EMAIL>');

    // Wait for error message
    await waitFor(() => {
      expect(
        screen.getByText('This email is already registered. Please use a different email address.'),
      ).toBeInTheDocument();
    });
  });

  it('should validate email on blur', async () => {
    renderComponent();
    const emailInput = screen.getByPlaceholderText('Enter email address');

    // Type email without triggering validation
    fireEvent.change(emailInput, {target: {value: '<EMAIL>'}});

    // Trigger blur event
    fireEvent.blur(emailInput);

    // Should show loading state
    expect(screen.getByText('Validating email...')).toBeInTheDocument();

    // Wait for validation to complete
    await waitFor(() => {
      expect(screen.queryByText('Validating email...')).not.toBeInTheDocument();
    });
  });

  it('should clear error when email field is emptied', async () => {
    renderComponent();
    const emailInput = screen.getByPlaceholderText('Enter email address');

    // Type existing email to trigger error
    await userEvent.type(emailInput, '<EMAIL>');

    // Wait for error message
    await waitFor(() => {
      expect(
        screen.getByText('This email is already registered. Please use a different email address.'),
      ).toBeInTheDocument();
    });

    // Clear the field
    await userEvent.clear(emailInput);

    // Error should be cleared
    expect(
      screen.queryByText('This email is already registered. Please use a different email address.'),
    ).not.toBeInTheDocument();
  });

  it('should validate email format before making API call', async () => {
    renderComponent();
    const emailInput = screen.getByPlaceholderText('Enter email address');

    // Type invalid email
    await userEvent.type(emailInput, 'invalid-email');

    // Should show format error without making API call
    await waitFor(() => {
      expect(screen.getByText('Invalid email address')).toBeInTheDocument();
    });
    expect(screen.queryByText('Validating email...')).not.toBeInTheDocument();
  });
});
