import {Box, styled, Typography} from '@mui/material';
import ConvertCheckIcon from 'Assets/ConvertCheckIcon';
import InvalidIcon from 'Assets/InvalidIcon';
import BorderButton from 'Components/BorderButton/BorderButton';
import Button from 'Components/Button';
import {DefaultDialog} from 'Components/DefaultDialog/DefaultDialog';
import {Integers} from 'Helpers/integers';

interface DialogProps {
  open: boolean;
  actionType: string;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
}

export const IconWrapper = styled(Box)(() => ({
  display: 'flex',
  justifyContent: 'center',
}));

export const KeyIconContainer = styled(Box)(({theme}) => {
  return {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '5rem',
    height: '5rem',
    borderRadius: '50%',
    border: `1px solid ${theme.palette.white[Integers.TwoHundred]}`,
    padding: '1rem',
    backgroundColor: theme.palette.alert.error.bg,
    fill: 'transparent',
    color: theme.palette.alert.success.bg,
  };
});

/**
 * Displays a dialog for confirming actions on a Pending Tenant, such as converting to a Tenant or marking as invalid.
 *
 * @param open - Controls whether the dialog is open.
 * @param actionType - The type of action to confirm ('convert' or 'invalid').
 * @param onClose - Callback fired when the dialog is closed.
 * @param onConfirm - Callback fired when the confirm action is taken.
 * @param title - Additional title or message to display in the dialog.
 * @param isLoading - Indicates if the confirm action is in a loading state.
 *
 * @returns A dialog component prompting the user to confirm the selected action.
 */
export const UserValidityDialog = ({open, actionType, onClose, onConfirm, title}: DialogProps) => {
  const isActionConvert = actionType === 'convert';
  const icon = isActionConvert ? (
    <ConvertCheckIcon sx={{fill: 'body.500', color: 'alert.success.onBg'}} />
  ) : (
    <InvalidIcon sx={{fill: 'body.500', color: 'alert.error.onBg'}} />
  );
  const actionMessage =
    'This username/email combination already exists in Scada System. So we are auto filling the username/email for you.';

  const iconBGColor = isActionConvert ? 'alert.success.bg' : 'alert.error.bg';

  return (
    <DefaultDialog title={'User Validity'} maxWidth={400} open={open} onClose={onClose}>
      <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4, p: 2}}>
        <IconWrapper>
          <KeyIconContainer sx={{backgroundColor: iconBGColor}}>{icon}</KeyIconContainer>
        </IconWrapper>

        <Typography sx={{mt: 2, fontSize: '1.125rem', fontWeight: 700, color: 'body.dark', textAlign: 'center'}}>
          {actionMessage}
        </Typography>
        <Typography
          sx={{
            mt: 1,
            fontSize: '0.813rem',
            fontWeight: 600,
            color: 'body.500',
            textAlign: 'center',
          }}
        >
          {title}
        </Typography>
        <Box
          sx={{mt: 4, display: 'flex', gap: 1, flexDirection: 'row', width: '100%', fontSize: '1rem', fontWeight: 600}}
        >
          <BorderButton sx={{flex: 1, color: 'body.dark', fontWeight: '600', fontSize: '1rem'}} onClick={onClose}>
            Cancel
          </BorderButton>
          <Button
            variant="contained"
            sx={{
              borderColor: 'body.100',
              borderRadius: '0.375rem',
              color: 'other.white',
              fontWeight: '600',
              fontSize: '1rem',
              backgroundColor: 'secondary.main',
              flex: 1,
              py: '0.875rem',
              px: '1.75rem',
            }}
            onClick={onConfirm}
            data-testid={'dialog-invalid-button'}
          >
            Continue
          </Button>
        </Box>
      </Box>
    </DefaultDialog>
  );
};
