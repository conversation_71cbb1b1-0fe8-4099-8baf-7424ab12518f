import {Box, OutlinedInput} from '@mui/material';
import AddUsersIcon from 'Assets/AddUsersIcon';
import DisabledRemoveIcon from 'Assets/DisabledRemoveIcon';
import RemoveUsersIcon from 'Assets/RemoveUsersIcon';
import {Integers} from 'Helpers/integers';
import {debounce} from 'lodash';
import {enqueueSnackbar} from 'notistack';
import {useEffect, useRef, useState} from 'react';

const minContentWidth = 74;
const maxContentWidth = 200;
const maxUserCount = 899; // based on the configuration that we have on stripe.
// The maximum limit for total cost of the plan shouldn't be more than 99999999 i.e eight character max
// So if we consider a max plan value of of $1000000 and cost per user of $100 then the max user count can be 899

export const StepCounter: React.FC<{userCount: number; setUserCount: (count: number) => void}> = ({
  userCount,
  setUserCount,
}) => {
  const [inputWidth, setInputWidth] = useState(minContentWidth); // start with your fixed width
  const spanRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    if (spanRef.current) {
      const width = spanRef.current.offsetWidth + Integers.Forty; // add padding for cursor
      setInputWidth(Math.min(Math.max(minContentWidth, width), maxContentWidth)); // keep min 74px
    }
  }, [userCount]);

  const showMaxUserWarning = (count: number) => {
    if (count > maxUserCount) {
      enqueueSnackbar(`Maximum user limit of allowed users is ${maxUserCount}`, {variant: 'error'});
    }
  };

  const debouncedShowMaxUserWarning = debounce(showMaxUserWarning, Integers.ThreeHundred);

  // temporaryToZero is used to handle the case when user clears the input field or enters 0
  // In both cases we don't want to call setUserCount until user blurs the input field or enters a valid number
  // This is to prevent the input field from jumping back to the last valid number while user is still typing
  // Once user blurs the input field or enters a valid number, we reset temporaryToZero to false and update the userCount
  const [temporaryToZero, setTemporaryToZero] = useState(false);

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        height: '100%',
        minHeight: 40,
      }}
    >
      {userCount === 1 ? (
        <DisabledRemoveIcon data-testid="disable-remove-icon" sx={{fontSize: '2.375rem', color: 'white.main'}} />
      ) : (
        <RemoveUsersIcon
          data-testid="decrease-user-count"
          onClick={() => setUserCount(Math.max(1, userCount - 1))}
          sx={{
            fontSize: '2.375rem',
            color: 'primary.main',
            '&:hover': {
              cursor: 'pointer',
            },
          }}
        />
      )}
      <Box
        sx={{
          width: inputWidth,
          height: Integers.ThirtyEight,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderWidth: '0.0625rem',
          borderStyle: 'solid',
          borderColor: 'divider',
          borderRadius: 1,
          bgcolor: 'white.main',
          overflow: 'hidden',
        }}
      >
        <OutlinedInput
          sx={{fontWeight: 800, maxHeight: 1, m: 0, p: 0, textAlign: 'center'}}
          id="user-count"
          value={temporaryToZero ? '' : userCount}
          inputProps={{
            sx: {
              textAlign: 'center',
            },
          }}
          onBlur={() => {
            if (temporaryToZero) {
              setTemporaryToZero(false);
            }
          }}
          onChange={e => {
            const value = e.target.value;
            if (value === '' || Number(value) === 0) {
              setTemporaryToZero(true);
              return;
            }
            if (Number(value) > maxUserCount) {
              debouncedShowMaxUserWarning(Number(value));
            }
            const newNumber = Number(value);
            // ✅ enforce min/max range
            if (isNaN(newNumber) || newNumber < 1 || newNumber > maxUserCount) return;
            setTemporaryToZero(false);
            debouncedShowMaxUserWarning(newNumber);
            setUserCount(newNumber);
          }}
        />
        {/* Hidden span to measure text width */}
        <span
          ref={spanRef}
          style={{
            position: 'absolute',
            visibility: 'hidden',
            whiteSpace: 'pre',
            fontWeight: 800,
            fontSize: '1rem',
          }}
        >
          {userCount}
        </span>
      </Box>
      <AddUsersIcon
        data-testid="increase-user-count"
        onClick={() => {
          const newValue = userCount + 1;
          if (newValue > maxUserCount) return;
          debouncedShowMaxUserWarning(newValue);
          setUserCount(Math.min(maxUserCount, userCount + 1));
        }}
        disabled={userCount >= maxUserCount}
        sx={{
          color: 'primary.main',
          fontSize: '2.375rem',
          '&:hover': {
            cursor: 'pointer',
          },
        }}
      />
    </Box>
  );
};
