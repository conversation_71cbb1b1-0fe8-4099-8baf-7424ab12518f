// StepCounter.test.tsx

import {fireEvent, screen} from '@testing-library/react';
import {describe, expect, it} from 'vitest';
import {StepCounter} from './Stepper';

// Use a simple render helper; replace with memoryRenderWithTheme if available in your project
import {render} from '@testing-library/react';
vi.mock('notistack', () => ({enqueueSnackbar: vi.fn()}));

describe('StepCounter', () => {
  it('renders with initial userCount', () => {
    const setUserCount = vi.fn();
    render(<StepCounter userCount={5} setUserCount={setUserCount} />);
    expect(screen.getByDisplayValue('5')).toBeInTheDocument();
  });

  it('calls setUserCount with incremented value when increase button is clicked', () => {
    const setUserCount = vi.fn();
    render(<StepCounter userCount={5} setUserCount={setUserCount} />);
    fireEvent.click(screen.getByTestId('increase-user-count'));
    expect(setUserCount).toHaveBeenCalledWith(6);
  });

  it('calls setUserCount with decremented value when decrease button is clicked', () => {
    const setUserCount = vi.fn();
    render(<StepCounter userCount={5} setUserCount={setUserCount} />);
    fireEvent.click(screen.getByTestId('decrease-user-count'));
    expect(setUserCount).toHaveBeenCalledWith(4);
  });

  it('does not decrement below 1 and shows disabled icon', () => {
    const setUserCount = vi.fn();
    render(<StepCounter userCount={1} setUserCount={setUserCount} />);
    expect(screen.getByTestId('disable-remove-icon')).toBeInTheDocument();
    expect(screen.queryByTestId('decrease-user-count')).not.toBeInTheDocument();
    fireEvent.click(screen.getByTestId('increase-user-count'));
    expect(setUserCount).toHaveBeenCalledWith(2);
  });

  it('does not increment above maxUserCount', () => {
    const setUserCount = vi.fn();
    render(<StepCounter userCount={899} setUserCount={setUserCount} />);
    fireEvent.click(screen.getByTestId('increase-user-count'));
    expect(setUserCount).not.toHaveBeenCalled();
  });

  it('sets userCount to 1 if input is cleared', () => {
    const setUserCount = vi.fn();
    render(<StepCounter userCount={5} setUserCount={setUserCount} />);
    const input = screen.getByDisplayValue('5');
    fireEvent.change(input, {target: {value: ''}});
    expect(setUserCount).not.toBeCalled();
    fireEvent.blur(input);
    expect(screen.getByDisplayValue('5')).toBeInTheDocument(); // should reset to original userCount
  });

  it('does not call setUserCount for invalid input (non-numeric, <1, >899)', () => {
    const setUserCount = vi.fn();
    render(<StepCounter userCount={5} setUserCount={setUserCount} />);
    const input = screen.getByDisplayValue('5');
    fireEvent.change(input, {target: {value: 'abc'}});
    fireEvent.change(input, {target: {value: '0'}});
    fireEvent.change(input, {target: {value: '900'}});
    expect(setUserCount).not.toHaveBeenCalled();
  });

  it('calls setUserCount for valid numeric input within range', () => {
    const setUserCount = vi.fn();
    render(<StepCounter userCount={5} setUserCount={setUserCount} />);
    const input = screen.getByDisplayValue('5');
    fireEvent.change(input, {target: {value: '10'}});
    expect(setUserCount).toHaveBeenCalledWith(10);
  });

  it('shows temporary zero state and resets on blur', () => {
    const setUserCount = vi.fn();
    render(<StepCounter userCount={5} setUserCount={setUserCount} />);
    const input = screen.getByDisplayValue('5');
    fireEvent.change(input, {target: {value: ''}});
    // Should show '0' in the input (temporaryToZero)
    expect(screen.getByDisplayValue('')).toBeInTheDocument();
    fireEvent.blur(input);
    // After blur, temporaryToZero should reset (input returns to userCount)
    expect(screen.getByDisplayValue('5')).toBeInTheDocument();
  });
});
