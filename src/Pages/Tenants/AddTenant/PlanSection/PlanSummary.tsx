import {Box, Paper, Stack, Typography} from '@mui/material';
import React from 'react';
import {PlanResponse} from 'redux/app/types/plan.type';
import {formatTrialPeriod} from './FreeTrialPlan/FreeTrialCard.utils';
import {StepCounter} from './Stepper';

export interface PlanSummaryProps {
  selectedPlan: PlanResponse;
  tenurePeriod: string;
  userCount: number;
  setUserCount: (count: number) => void;
  usersCost: number;
  totalCost: number;
  fmt: (n: number) => string;
  trialSelected?: boolean;
}
const BORDER_BOTTOM = '0.125rem dotted';
const secondaryDark = 'body.dark';
const secondaryLight = 'secondary.200';

/**
 * Displays a summary of the selected plan, including user count controls (if applicable),
 * plan details, and a breakdown of costs.
 *
 * @param selectedPlan - The plan currently selected by the user.
 * @param tenurePeriod - The billing period for the selected plan (e.g., "Monthly", "Yearly").
 * @param userCount - The current number of users specified for the tenant.
 * @param setUserCount - Callback to update the user count.
 * @param usersCost - The calculated total cost for all users.
 * @param totalCost - The overall total cost including plan and user costs.
 * @param fmt - Function to format cost values for display.
 *
 * @remarks
 * - If the selected plan does not allow unlimited users, a user count selector is shown.
 * - The component displays a price summary, including plan cost, user cost, and total cost.
 * - Uses Material UI components for layout and styling.
 */
const PlanSummary: React.FC<PlanSummaryProps> = ({
  selectedPlan,
  tenurePeriod,
  userCount,
  setUserCount,
  usersCost,
  totalCost,
  fmt,
  trialSelected,
}) => {
  return (
    <Stack spacing={3} sx={{mt: 2}}>
      {!trialSelected && !selectedPlan.allowedUnlimitedUsers && (
        <Paper
          variant="outlined"
          sx={{p: 2, borderRadius: 1, borderColor: 'secondary.100', backgroundColor: 'secondary.25'}}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: 2,
              flexWrap: 'nowrap',
            }}
          >
            <Box sx={{minWidth: 0}}>
              <Typography
                variant="subtitle1"
                sx={{fontWeight: 700, fontSize: '1rem', lineHeight: '1.25rem', color: secondaryDark}}
              >
                Number of Users
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  mt: 0.5,
                  fontWeight: 400,
                  fontSize: '0.875rem',
                  lineHeight: '1.125rem',
                  color: 'body.600',
                }}
              >
                {`Enter the number of users for this tenant. The total cost will be calculated based on the selected plan's “Cost per User.”`}
              </Typography>
            </Box>
            <Box sx={{alignSelf: 'stretch', display: 'flex', alignItems: 'center'}}>
              <StepCounter userCount={userCount} setUserCount={setUserCount} />
            </Box>
          </Box>
        </Paper>
      )}

      <Paper
        variant="outlined"
        sx={{p: 2, borderRadius: 1, borderColor: 'secondary.100', backgroundColor: 'secondary.25'}}
      >
        <Typography
          variant="subtitle1"
          sx={{fontWeight: 700, fontSize: '1rem', lineHeight: '1.25rem', color: secondaryDark}}
        >
          Price summary
        </Typography>
        <Typography
          variant="body2"
          sx={{mt: 0.5, color: 'body.500', fontWeight: 600, fontSize: '0.875rem', lineHeight: '1.125rem'}}
        >
          {selectedPlan?.name} - {tenurePeriod}
        </Typography>
        {trialSelected && (
          <Box sx={{display: 'flex', alignItems: 'center', py: 1, mt: 1.5}}>
            <Typography
              variant="body1"
              sx={{color: 'body.800', fontWeight: 600, fontSize: '0.875rem', lineHeight: '1.125rem'}}
            >
              Free trial
            </Typography>
            <Box sx={{flex: 1, borderBottom: BORDER_BOTTOM, borderColor: secondaryLight, mx: 2}} />
            <Typography
              variant="body1"
              data-testid="plan-cost"
              sx={{fontWeight: 700, fontSize: '0.875rem', lineHeight: '1.125rem', color: secondaryDark}}
            >
              {formatTrialPeriod(new Date().toISOString(), Number(selectedPlan.metaData?.trialDays))}
            </Typography>
          </Box>
        )}
        <Box sx={{display: 'flex', alignItems: 'center', py: 1, mt: 1.5}}>
          <Typography
            variant="body1"
            sx={{color: 'body.800', fontWeight: 600, fontSize: '0.875rem', lineHeight: '1.125rem'}}
          >
            Plan cost
          </Typography>
          <Box sx={{flex: 1, borderBottom: BORDER_BOTTOM, borderColor: secondaryLight, mx: 2}} />
          <Typography
            variant="body1"
            data-testid="plan-cost"
            sx={{fontWeight: 700, fontSize: '0.875rem', lineHeight: '1.125rem', color: secondaryDark}}
          >
            {selectedPlan?.price}
          </Typography>
        </Box>
        <Box sx={{display: 'flex', alignItems: 'center', py: 1}}>
          <Typography
            variant="body1"
            sx={{color: 'body.800', fontWeight: 600, fontSize: '0.875rem', lineHeight: '1.125rem'}}
          >
            Total cost for all users
          </Typography>
          <Box sx={{flex: 1, borderBottom: BORDER_BOTTOM, borderColor: secondaryLight, mx: 2}} />
          <Typography data-testid="users-cost" variant="body1" sx={{fontWeight: 700}}>
            {fmt(usersCost)}
          </Typography>
        </Box>
        <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 1.5}}>
          <Typography
            variant="subtitle1"
            sx={{fontWeight: 700, fontSize: '0.875rem', lineHeight: '1.125rem', color: secondaryDark}}
          >
            Total cost
          </Typography>
          <Typography
            data-testid="total-cost"
            variant="h6"
            sx={{fontWeight: 800, fontSize: '1.375rem', lineHeight: '1.875rem', color: 'secondary.900'}}
          >
            {fmt(totalCost)}
            {!trialSelected && (
              <Typography
                variant="subtitle1"
                component="span"
                sx={{color: 'secondary.700', fontWeight: 500, fontSize: '0.875rem', lineHeight: '1.125rem'}}
              >
                /{tenurePeriod.slice(0, -2)}
              </Typography>
            )}
          </Typography>
        </Box>
      </Paper>
    </Stack>
  );
};

export default PlanSummary;
