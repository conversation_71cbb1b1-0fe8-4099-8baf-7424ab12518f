import {differenceInDays} from 'date-fns';
import {Subscription, SubscriptionStatus} from 'redux/app/types';

export interface TrialInfo {
  startDate: string;
  endDate: string;
  daysRemaining: number;
  isExpired: boolean;
}

export function getTrialInfo(subsc: Subscription): TrialInfo | null {
  if (!subsc?.createdOn || !subsc?.trialEndDate) {
    return null; // no trial info available
  }

  const startDate = subsc.createdOn;
  const endDate = subsc.trialEndDate;
  const today = new Date();

  const daysRemaining = differenceInDays(endDate, today);
  const isExpired =
    subsc.status === SubscriptionStatus.TRIAL_SUSPEND || subsc.status === SubscriptionStatus.TRIAL_EXPIRED;

  return {
    startDate: startDate, // only date
    endDate: endDate, // only date
    daysRemaining: daysRemaining < 0 ? 0 : daysRemaining,
    isExpired,
  };
}

// Utility to format date in short form
export const formatTrialPeriod = (startDate: string, endDateOrDays?: string | number): string => {
  const start = new Date(startDate);

  // If second arg is number, calculate endDate
  const end =
    typeof endDateOrDays === 'number'
      ? new Date(start.getTime() + endDateOrDays * 24 * 60 * 60 * 1000)
      : new Date(endDateOrDays ?? startDate); // fallback to startDate if nothing given

  const sameYear = start.getFullYear() === end.getFullYear();

  const startFormatted = start.toLocaleDateString('en-GB', {
    day: 'numeric',
    month: 'short',
    ...(sameYear ? {} : {year: 'numeric'}),
  });

  const endFormatted = end.toLocaleDateString('en-GB', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  });

  return `${startFormatted} - ${endFormatted}`;
};
