// FreeTrialCard.test.tsx
import {ThemeProvider, createTheme} from '@mui/material/styles';
import {render, screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {FormAddPlan} from 'Pages/PlansPage/AddPlan';
import {SubscriptionStatus} from 'redux/app/types';
import {PlanResponse} from 'redux/app/types/plan.type';
import {describe, expect, it, vi} from 'vitest';
import FreeTrialCard from './FreeTrialCard';

// Mock utils
vi.mock('./FreeTrialCard.utils', () => ({
  formatTrialPeriod: vi.fn((start, end) => `Formatted: ${start} - ${end}`),
  getTrialInfo: vi.fn(info => ({
    ...info,
    isExpired: info.isExpired,
    daysRemaining: info.daysRemaining ?? 10,
    startDate: info.startDate ?? '2025-09-01',
    endDate: info.endDate ?? '2025-09-10',
  })),
}));

// Dummy trial plan
const mockPlan: FormAddPlan = {
  id: 'p1',
  tier: 'Premium',
  price: 100,
  metaData: {trialDays: 14},
  configureDevice: {
    min: 1,
    max: 5,
    computeSize: '',
    dbSize: '',
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '',
    modifiedOn: null,
    createdBy: '',
    modifiedBy: null,
    id: '',
  },
  name: '',
  configureDeviceId: '',
  billingCycleId: '',
  allowedUnlimitedUsers: false,
};

function renderWithTheme(ui: React.ReactNode) {
  const theme = createTheme({
    palette: {
      plan: {
        normal: ['#111', '#222'],
        selected: ['#333', '#444'],
      },
      alert: {
        error: {border: '#f00', bg: '#fee', onBg: '#900', main: '#f00'},
        success: {main: '#0f0', onBg: '#060'},
      },
      body: {100: '#ddd', dark: {600: '#333'}},
      background: {default: '#fff'},
    },
  } as any);

  return render(<ThemeProvider theme={theme}>{ui}</ThemeProvider>);
}

describe('FreeTrialCard', () => {
  it('renders default view with trial plan', () => {
    renderWithTheme(<FreeTrialCard trialPlanDetails={mockPlan} />);
    expect(screen.getByText(/Start with Free trial/i)).toBeInTheDocument();
    expect(screen.getByText(/14 days free trial plan/i)).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).toBeEnabled();
  });

  it('switch is disabled when no plan is provided', () => {
    renderWithTheme(<FreeTrialCard />);
    expect(screen.getByRole('checkbox')).toBeDisabled();
  });

  it('calls onSelectTrial when switch toggled', async () => {
    const user = userEvent.setup();
    const onSelectTrial = vi.fn();
    renderWithTheme(<FreeTrialCard trialPlanDetails={mockPlan} onSelectTrial={onSelectTrial} />);
    const checkbox = screen.getByRole('checkbox');
    await user.click(checkbox);
    expect(onSelectTrial).toHaveBeenCalledWith(true, mockPlan);
    await user.click(checkbox);
    expect(onSelectTrial).toHaveBeenCalledWith(true, mockPlan);
  });

  it('renders edit mode with active trial info', () => {
    renderWithTheme(
      <FreeTrialCard
        isEdit
        trialSubscriptionInfo={{
          id: 'sub-1',
          createdOn: '2025-09-01T00:00:00Z',
          trialEndDate: '2025-09-10T00:00:00Z',
          status: SubscriptionStatus.ACTIVE,
          // fill rest with dummy values
          deleted: false,
          deletedOn: null,
          deletedBy: null,
          plan: mockPlan as unknown as PlanResponse,
          modifiedOn: '2025-09-01T00:00:00Z',
          createdBy: 'user-1',
          modifiedBy: null,
          subscriberId: 'tenant-1',
          startDate: '2025-09-01T00:00:00Z',
          endDate: '2025-12-01T00:00:00Z',
          planId: 'plan-1',
          tierId: 'tier-1',
          clusterId: 'cl-1',
          tenantId: 'tenant-1',
          numberOfUsers: 10,
          totalCost: 0,
          tier: {} as any,
          tagId: 'tag-1',
        }}
        trialPlanDetails={mockPlan}
      />,
    );

    expect(screen.getByText(/10 days left/i)).toBeInTheDocument();
    expect(screen.getByText(/Active/i)).toBeInTheDocument();
  });

  it('toggles details section when clicking "View details"', async () => {
    const user = userEvent.setup();
    renderWithTheme(<FreeTrialCard trialPlanDetails={mockPlan} />);
    expect(screen.queryByText(/Infra configuration/i)).not.toBeInTheDocument();

    const viewDetails = screen.getByText(/View details/i);
    await user.click(viewDetails);
    expect(screen.getByText(/Infra configuration/i)).toBeInTheDocument();
    expect(screen.getByText(/\$100/)).toBeInTheDocument();
    expect(screen.getByText(/1–5/)).toBeInTheDocument();

    await user.click(viewDetails);
    expect(screen.queryByText(/Infra configuration/i)).not.toBeInTheDocument();
  });

  it('shows "Trial plan not found" if details opened without plan', async () => {
    const user = userEvent.setup();
    renderWithTheme(<FreeTrialCard />);
    const viewDetails = screen.getByText(/View details/i);
    await user.click(viewDetails);
    expect(screen.getByText(/Trial plan not found/i)).toBeInTheDocument();
  });
});
