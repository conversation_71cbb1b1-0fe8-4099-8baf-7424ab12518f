import {Box, Chip, Divider, Grid, Stack, Typography} from '@mui/material';
import {styled, useTheme} from '@mui/material/styles';
import Switch from '@mui/material/Switch';
import {Integers} from 'Helpers/integers';
import {FormAddPlan} from 'Pages/PlansPage/AddPlan';
import {useEffect, useState} from 'react';
import {Subscription} from 'redux/app/types';
import {formatTrialPeriod, getTrialInfo, TrialInfo} from './FreeTrialCard.utils';
const SPACE_BETWEEN = 'space-between';
const INLINE_BLOCK = 'inline-block';
function getBorderColour(isEdit: boolean | undefined, isExpired: boolean | undefined) {
  return isEdit && isExpired ? 'alert.error.border' : 'body.100';
}

function getHeaderText(isEdit: boolean | undefined, name?: string) {
  return isEdit ? name : 'Start with free trial';
}
function getStatusChip(isEdit: boolean | undefined, isExpired: boolean | undefined) {
  return (
    isEdit &&
    (isExpired ? (
      <Chip
        label={
          <Stack direction="row" spacing={0.5} alignItems="center">
            <Box
              sx={{
                width: 6,
                height: 6,
                borderRadius: '50%',
                bgcolor: 'alert.error.main', // dot color
              }}
            />
            <Typography
              sx={{
                color: 'alert.error.onBg', // text color
                fontWeight: 700,
                fontSize: '0.75rem',
              }}
            >
              Expired
            </Typography>
          </Stack>
        }
        color="error"
        size="small"
        sx={{
          backgroundColor: 'background.default',
          borderRadius: '4px',
          px: '0.5rem', // padding for better look
          marginLeft: '0.5rem',
        }}
      />
    ) : (
      <Chip
        label={
          <Stack direction="row" spacing={0.5} alignItems="center">
            <Box
              sx={{
                width: 6,
                height: 6,
                borderRadius: '50%',
                bgcolor: 'alert.success.main', // dot color
              }}
            />
            <Typography
              sx={{
                color: 'alert.success.onBg', // text color
                fontWeight: 500,
                fontSize: '0.75rem',
              }}
            >
              Active
            </Typography>
          </Stack>
        }
        color="success"
        size="small"
        sx={{
          backgroundColor: '#dcfce7',
          borderRadius: '4px',
          px: 0.5, // padding for better look
          marginLeft: '0.5rem',
        }}
      />
    ))
  );
}
function getBackgroundColour(
  isEdit: boolean | undefined,
  isExpired: boolean | undefined,
  trialSelected: boolean | undefined,
  selectedGradient: string,
  gradient: string,
) {
  if (isEdit && isExpired) return {backgroundColor: 'alert.error.bg'};
  else if (isEdit) return {backgroundImage: selectedGradient};
  else {
    return {backgroundImage: trialSelected ? selectedGradient : gradient};
  }
}
function getColor(isEdit: boolean | undefined, isExpired: boolean | undefined) {
  return isEdit && isExpired ? 'alert.error.onBg' : 'body.dark.600';
}

function getSubsHeading(isEdit: boolean | undefined, trialInfo: TrialInfo | null, trialPlan: FormAddPlan | undefined) {
  return isEdit
    ? `${trialInfo?.daysRemaining} days left`
    : `Tenant will begin with a ${trialPlan?.metaData?.trialDays} days free trial plan.`;
}
function getWeight(isEdit: boolean | undefined) {
  return isEdit ? Integers.SixHundred : Integers.FourHundred;
}

const AntSwitch = styled(Switch)(({theme}) => ({
  width: '2.5rem', // 40px
  height: '1.375rem', // 22px
  padding: 0,
  display: 'flex',
  '&:active': {
    '& .MuiSwitch-thumb': {
      width: '1.25rem', // 20px thumb on active press
    },
    '& .MuiSwitch-switchBase.Mui-checked': {
      transform: 'translateX(1.125rem)', // adjust for bigger switch
    },
  },
  '& .MuiSwitch-switchBase': {
    padding: '0.25rem',
    '&.Mui-checked': {
      transform: 'translateX(1.125rem)', // checked position
      color: '#fff',
      '& + .MuiSwitch-track': {
        opacity: 1,
        backgroundColor: '#7B1E45', // maroon
      },
    },
  },
  '& .MuiSwitch-thumb': {
    boxShadow: '0 2px 4px 0 rgb(0 35 11 / 20%)',
    width: '1rem', // 16px thumb size
    height: '1rem', // 16px thumb size
    borderRadius: '50%',
    backgroundColor: '#fff',
    transition: theme.transitions.create(['width'], {
      duration: 200,
    }),
  },
  '& .MuiSwitch-track': {
    borderRadius: '0.75rem', // round track
    opacity: 1,
    backgroundColor: 'rgba(0,0,0,.25)',
    boxSizing: 'border-box',
  },
}));
interface FreeTrialCardProps {
  onSelectTrial?: (isSelected: boolean, plan?: FormAddPlan | null) => void;
  trialSelected?: boolean;
  isEdit?: boolean;
  trialSubscriptionInfo?: Subscription;
  trialPlanDetails?: FormAddPlan;
}
export default function FreeTrialCard({
  onSelectTrial,
  trialSelected,
  isEdit,
  trialSubscriptionInfo,
  trialPlanDetails,
}: Readonly<FreeTrialCardProps>) {
  const [showDetails, setShowDetails] = useState(false);
  const [trialPlan, setTrialPlan] = useState<FormAddPlan>();
  useEffect(() => {
    if (trialPlanDetails) {
      setTrialPlan(trialPlanDetails);
    }
  }, [trialPlanDetails]);

  const trialInfo = trialSubscriptionInfo ? getTrialInfo(trialSubscriptionInfo) : null;

  const theme = useTheme();
  const getGradientColor = (color1: string, color2: string) =>
    `linear-gradient(145deg, ${color1} 1.31%, ${color2} 97.7%)`;

  // Use the planGradient and planSelectedGradient from theme colors
  const gradient = getGradientColor(theme.palette.plan.normal[Integers.One], theme.palette.plan.normal[Integers.Two]);
  const selectedGradient = getGradientColor(
    theme.palette.plan.selected[Integers.One],
    theme.palette.plan.selected[Integers.Two],
  );
  function handleSwitchChange(event: React.ChangeEvent<HTMLInputElement>) {
    if (onSelectTrial) {
      onSelectTrial(event.target.checked, event.target.checked ? trialPlan : null);
    }
  }

  return (
    <Grid
      container
      sx={{
        minWidth: '100%',
        minHeight: '4.8125rem',
        opacity: 1,
        padding: '1rem',
        marginBottom: '1.5rem',
        borderRadius: '0.375rem',
        borderWidth: '0.0625rem',
        borderStyle: 'solid', // optional if you want visible border
        borderColor: getBorderColour(isEdit, trialInfo?.isExpired),
        alignItems: 'center', // vertically center children
        justifyContent: SPACE_BETWEEN,

        ...(getBackgroundColour(isEdit, trialInfo?.isExpired, trialSelected, selectedGradient, gradient) as object),
      }}
    >
      <Grid
        container
        size={{xs: 12}}
        sx={{
          alignItems: 'center', // vertically center children
          justifyContent: SPACE_BETWEEN,
        }}
      >
        <Grid container size={{sm: 4}} sx={{maxHeight: '2.625rem'}}>
          <Grid
            size={{xs: 12}}
            sx={{
              fontFamily: 'Lato',
              fontWeight: 700,
              fontStyle: 'normal', // "Bold" is covered by fontWeight
              fontSize: '1rem', // 16px
              lineHeight: '1.25rem', // 20px
              letterSpacing: '0.01em', // ~1%
              verticalAlign: 'middle',
            }}
          >
            {getHeaderText(isEdit, trialPlan?.name)}
            {getStatusChip(isEdit, trialInfo?.isExpired)}
          </Grid>
          <Grid
            size={{xs: 12}}
            sx={{
              fontFamily: 'Lato',
              fontWeight: getWeight(isEdit),
              fontStyle: 'normal', // "Regular" → normal
              fontSize: '0.875rem', // 14px
              lineHeight: '1.125rem', // 18px
              letterSpacing: '0.02em', // ~2%
              color: getColor(isEdit, trialInfo?.isExpired),
            }}
          >
            {getSubsHeading(isEdit, trialInfo, trialPlan)}
          </Grid>
        </Grid>

        <Grid
          container
          size={{sm: 6}}
          direction="column"
          alignItems="flex-start"
          sx={{
            flexDirection: 'column', // stack children vertically
            justifyContent: 'flex-end', // push them to the bottom
            alignItems: 'flex-end',
          }}
        >
          {/* Top row: switch + label */}
          <Grid container alignItems="center" wrap="nowrap" sx={{mb: 0.5}}>
            {isEdit && trialInfo ? (
              <strong>{formatTrialPeriod(trialInfo.startDate, trialInfo.endDate)}</strong>
            ) : (
              <>
                <AntSwitch
                  inputProps={{'aria-label': 'ant design'}}
                  onChange={handleSwitchChange}
                  checked={trialSelected ?? false}
                  disabled={!trialPlan}
                />
                <Typography
                  sx={{
                    ml: 1,
                    fontFamily: 'Lato',
                    fontWeight: 700,
                    fontSize: '1rem', // 16px
                    lineHeight: '1.25rem', // 20px
                    letterSpacing: '0.01em',
                  }}
                >
                  {trialPlan?.metaData?.trialDays} days free trial
                </Typography>
              </>
            )}
          </Grid>

          {/* Bottom row: View details */}
          <Typography
            sx={{
              fontFamily: 'Lato',
              fontWeight: 400,
              fontSize: '0.875rem', // 14px
              lineHeight: '1.125rem', // 18px
              letterSpacing: '0.02em',
              color: '#1a3d7c',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
            }}
            onClick={() => setShowDetails(prev => !prev)}
          >
            <Box component="span" sx={{textDecoration: 'underline'}}>
              View details
            </Box>{' '}
            {showDetails ? '▲' : '▼'}
          </Typography>
        </Grid>
      </Grid>

      {/* Details Section */}
      {showDetails && (
        <>
          <Divider
            sx={{
              // width: "70.3125rem",   // 1125px
              // height: 0,
              // opacity: 0.1,
              borderWidth: '0.0625rem', // 1px
              borderColor: 'rgba(0, 0, 0, 0.1)', // light gray w/ opacity
              marginTop: '1rem',
              marginBottom: '1rem',
              width: '100%',
            }}
          />

          {trialPlan ? (
            <Grid
              container
              size={{xs: 12}}
              sx={{
                alignItems: 'center', // vertically center children
                justifyContent: SPACE_BETWEEN,
              }}
            >
              <Grid container size={{xs: 3}}>
                <Grid size={{xs: 12}}>
                  <Typography
                    sx={{
                      fontFamily: 'Lato',
                      fontWeight: 500,
                      fontStyle: 'normal', // "Medium" = weight 500
                      fontSize: '0.6875rem', // 11px
                      lineHeight: '0.8125rem', // 13px
                      letterSpacing: '0.02em', // 2%
                      verticalAlign: 'middle',
                    }}
                  >
                    Infra configuration
                  </Typography>
                </Grid>
                <Grid size={{xs: 12}}>
                  <Typography
                    sx={{
                      fontFamily: 'Lato',
                      fontWeight: 700, // Bold
                      fontStyle: 'normal',
                      fontSize: '0.875rem', // 14px
                      lineHeight: '1.125rem', // 18px
                      letterSpacing: '0.01em', // 1%
                      verticalAlign: 'middle',
                      display: INLINE_BLOCK, // ensures verticalAlign works
                    }}
                  >
                    {trialPlan.tier}
                  </Typography>
                  <span
                    style={{
                      fontFamily: 'Lato',
                      fontWeight: 500, // Medium
                      fontStyle: 'normal',
                      fontSize: '0.875rem', // 14px
                      lineHeight: '1.125rem', // 18px
                      letterSpacing: '0.01em',
                      verticalAlign: 'middle',
                      display: INLINE_BLOCK,
                      marginLeft: '0.25rem', // small gap between Premium and (…)
                    }}
                  >
                    (Pooled compute, Silo storage)
                  </span>
                </Grid>
              </Grid>

              <Grid container size={{xs: 3}}>
                <Grid size={{xs: 12}}>
                  <Typography
                    sx={{
                      fontFamily: 'Lato',
                      fontWeight: 500,
                      fontStyle: 'normal', // "Medium" = weight 500
                      fontSize: '0.6875rem', // 11px
                      lineHeight: '0.8125rem', // 13px
                      letterSpacing: '0.02em', // 2%
                      verticalAlign: 'middle',
                    }}
                  >
                    Cost
                  </Typography>
                </Grid>
                <Grid size={{xs: 12}}>
                  <Typography
                    sx={{
                      fontFamily: 'Lato',
                      fontWeight: 700, // Bold
                      fontStyle: 'normal',
                      fontSize: '0.875rem', // 14px
                      lineHeight: '1.125rem', // 18px
                      letterSpacing: '0.01em', // 1%
                      verticalAlign: 'middle',
                      display: INLINE_BLOCK, // ensures verticalAlign works
                    }}
                  >
                    ${trialPlan.price}
                  </Typography>
                </Grid>
              </Grid>

              <Grid container size={{xs: 3}}>
                <Grid size={{xs: 12}}>
                  <Typography
                    sx={{
                      fontFamily: 'Lato',
                      fontWeight: 500,
                      fontStyle: 'normal', // "Medium" = weight 500
                      fontSize: '0.6875rem', // 11px
                      lineHeight: '0.8125rem', // 13px
                      letterSpacing: '0.02em', // 2%
                      verticalAlign: 'middle',
                    }}
                  >
                    No. of devices
                  </Typography>
                </Grid>
                <Grid size={{xs: 12}}>
                  <Typography
                    sx={{
                      fontFamily: 'Lato',
                      fontWeight: 700, // Bold
                      fontStyle: 'normal',
                      fontSize: '0.875rem', // 14px
                      lineHeight: '1.125rem', // 18px
                      letterSpacing: '0.01em', // 1%
                      verticalAlign: 'middle',
                      display: INLINE_BLOCK, // ensures verticalAlign works
                    }}
                  >
                    {trialPlan.configureDevice?.min}–{trialPlan.configureDevice?.max}
                  </Typography>
                </Grid>
              </Grid>

              <Grid container size={{xs: 3}}>
                <Grid size={{xs: 12}}>
                  <Typography
                    sx={{
                      fontFamily: 'Lato',
                      fontWeight: 500,
                      fontStyle: 'normal', // "Medium" = weight 500
                      fontSize: '0.6875rem', // 11px
                      lineHeight: '0.8125rem', // 13px
                      letterSpacing: '0.02em', // 2%
                      verticalAlign: 'middle',
                    }}
                  >
                    Users
                  </Typography>
                </Grid>
                <Grid size={{xs: 12}}>
                  <Typography
                    sx={{
                      fontFamily: 'Lato',
                      fontWeight: 700, // Bold
                      fontStyle: 'normal',
                      fontSize: '0.875rem', // 14px
                      lineHeight: '1.125rem', // 18px
                      letterSpacing: '0.01em', // 1%
                      verticalAlign: 'middle',
                      display: INLINE_BLOCK, // ensures verticalAlign works
                    }}
                  >
                    1
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
          ) : (
            <Typography variant="body2" color="error">
              Trial plan not found
            </Typography>
          )}
        </>
      )}
    </Grid>
  );
}
