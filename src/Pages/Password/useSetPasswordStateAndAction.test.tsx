import {act, renderHook} from '@testing-library/react';
import {vi} from 'vitest';
import useSetPasswordStateAndAction, {Mode} from './useSetPasswordStateAndAction';

// Mock redux/auth/authApiSlice
const mockResetPassword = vi.fn();
const mockSetPassword = vi.fn();

vi.mock('redux/auth/authApiSlice', () => ({
  useResetPasswordMutation: () => [mockResetPassword, {isLoading: false}],
  useSetActivationPasswordMutation: () => [mockSetPassword, {isLoading: false}],
}));

describe('useSetPasswordStateAndAction', () => {
  beforeEach(() => {
    mockResetPassword.mockClear();
    mockSetPassword.mockClear();
  });

  it('returns correct strings for set-password mode', () => {
    const {result} = renderHook(() => useSetPasswordStateAndAction(Mode.SetPassword));
    expect(result.current.titleString[Mode.SetPassword]).toBe('Set Your password');
    expect(result.current.subTitleString[Mode.SetPassword]).toBe(
      'Create a strong password to get started with your account.',
    );
    expect(result.current.successMessagePart[Mode.SetPassword]).toBe('created');
    expect(result.current.submitButtonTitle[Mode.SetPassword]).toBe('Set Password');
    expect(result.current.tokenNotFoundErrorMessage[Mode.SetPassword]).toBe(
      'No token found. Please use the link from your set password email.',
    );
  });

  it('returns correct strings for reset-password mode', () => {
    const {result} = renderHook(() => useSetPasswordStateAndAction(Mode.ResetPassword));
    expect(result.current.titleString[Mode.ResetPassword]).toBe('Reset your password');
    expect(result.current.subTitleString[Mode.ResetPassword]).toBe('Enter a new password to reset your password');
    expect(result.current.successMessagePart[Mode.ResetPassword]).toBe('updated');
    expect(result.current.submitButtonTitle[Mode.ResetPassword]).toBe('Change Password');
    expect(result.current.tokenNotFoundErrorMessage[Mode.ResetPassword]).toBe(
      'No token found. Please use the link from your reset password email.',
    );
  });

  it('calls setPassword when mode is set-password', () => {
    const {result} = renderHook(() => useSetPasswordStateAndAction(Mode.SetPassword));
    const arg = {password: '123', token: 'abc', noCommonErrorHandler: true};
    act(() => {
      result.current.performPasswordAction(arg as any);
    });
    expect(mockSetPassword).toHaveBeenCalledWith(arg);
    expect(mockResetPassword).not.toHaveBeenCalled();
  });

  it('calls resetPassword when mode is reset-password', () => {
    const {result} = renderHook(() => useSetPasswordStateAndAction(Mode.ResetPassword));
    const arg = {password: '456', token: 'def', noCommonErrorHandler: true};
    act(() => {
      result.current.performPasswordAction(arg as any);
    });
    expect(mockResetPassword).toHaveBeenCalledWith(arg);
    expect(mockSetPassword).not.toHaveBeenCalled();
  });
});
