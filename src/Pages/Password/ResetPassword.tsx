import ReportProblemOutlinedIcon from '@mui/icons-material/ReportProblemOutlined';
import {Box, InputAdornment, List, ListItem, ListItemIcon, Typography} from '@mui/material';
import CheckwhiteIcon from 'Assets/CheckwhiteIcon';
import PasscodeLockIcon from 'Assets/Passcode-lock';
import ValidationRightBgIcon from 'Assets/ValidationRightBg';
import ValidationWrongBgIcon from 'Assets/ValidationWrongBg';
import Form from 'Components/Forms/Form';
import {useFormikContext} from 'formik';
import {Integers} from 'Helpers/integers';
import {AnyObject} from 'Helpers/utils';
import {isFetchBaseQueryError} from 'Hooks/useAuth';
import React, {useState} from 'react';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {
  IconWrapper,
  LogoContainer,
  signedInTextStyles,
  signInWrapper,
  WelcomeTitle,
} from 'styles/pages/ForgotPassword.styles';
import {
  iconStyles,
  KeyIconContainer,
  listItemCheckedTypographyStyles,
  listItemContainerStyles,
  listItemStyles,
  listItemTypographyStyles,
  passwordValidationStyles,
  typographyStyles,
} from 'styles/pages/ResetPassword.styles';
import * as Yup from 'yup';
import {
  errorIconStyles,
  errorStyles,
  FieldLabel,
  FormFieldContainer,
  HeaderContainer,
  IconDivider,
  inputStyles,
  LoginCard,
  LoginContainer,
  StyledButton,
  StyledDistekLogo,
  StyledFormPasswordInput,
  StyledLockIcon,
  SubTitle,
} from '../../styles/pages/Login.styles';
import useSetPasswordStateAndAction, {Mode} from './useSetPasswordStateAndAction';

const setPasswordValidationSchema = Yup.object({
  confirmPassword: Yup.string()
    .required('Confirm password is required')
    .oneOf([Yup.ref('newPassword')], 'Passwords must match'),
});

const initialValues = {
  newPassword: '',
  confirmPassword: '',
};

interface PageLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle: React.ReactNode;
  isPasswordSent?: boolean;
}

/**
 * PageLayout is a reusable layout component for authentication-related pages.
 * It displays a logo, an icon (lock or checkmark based on password sent status),
 * a title, a subtitle, and renders its children within a styled card.
 *
 * @param {React.PropsWithChildren<PageLayoutProps>} props - The props for the component.
 * @param {React.ReactNode} props.children - The content to be displayed inside the layout.
 * @param {string} props.title - The main title displayed at the top of the card.
 * @param {string} props.subtitle - The subtitle displayed below the title.
 * @param {boolean} [props.isPasswordSent=false] - Determines which icon and color to display;
 * if true, shows a success checkmark, otherwise shows a lock icon.
 *
 * @returns {JSX.Element} The rendered layout for the password reset page.
 */
const PageLayout: React.FC<PageLayoutProps> = ({children, title, subtitle, isPasswordSent = false}) => (
  <LoginContainer data-testid="ResetPasswordPage">
    <LoginCard elevation={0}>
      <HeaderContainer>
        <LogoContainer>
          <StyledDistekLogo />
        </LogoContainer>

        <IconWrapper>
          <KeyIconContainer backgroundColor={isPasswordSent ? 'alert.success.main' : undefined}>
            {isPasswordSent ? (
              <CheckwhiteIcon data-testid="check-icon" />
            ) : (
              <PasscodeLockIcon data-testid="lock-icon" width={40} height={40} />
            )}
          </KeyIconContainer>
        </IconWrapper>

        <WelcomeTitle variant="h4">{title}</WelcomeTitle>
        <SubTitle variant="h6">{subtitle}</SubTitle>
      </HeaderContainer>

      {children}
    </LoginCard>
  </LoginContainer>
);

interface PasswordRequirement {
  text: string;
  id: string;
  validator: (password: string) => boolean;
}

const passwordRequirements: PasswordRequirement[] = [
  {
    id: 'length',
    text: 'Minimum 8 characters',
    validator: (password: string) => password.length >= 8,
  },
  {
    id: 'lowercase',
    text: 'At least one uppercase letter',
    validator: (password: string) => /[A-Z]/.test(password),
  },
  {
    id: 'uppercase',
    text: 'At least one lowercase letter',
    validator: (password: string) => /[a-z]/.test(password),
  },
  {
    id: 'specialcharacter',
    text: 'At least one special character',
    validator: (password: string) => /[!@#$%^&*(),.?":{}|<>]/.test(password),
  },
  {
    id: 'number',
    text: 'At least one number',
    validator: (password: string) => /\d/.test(password),
  },
];

/**
 * Renders a list of password validation requirements with visual feedback.
 *
 * This component uses Formik context to access the current value of the new password,
 * and displays a list of requirements, each with an icon indicating whether the
 * requirement is satisfied.
 *
 * @returns {JSX.Element} The rendered password validation list.
 */
const PasswordValidationList = () => {
  const {values} = useFormikContext<{newPassword: string}>();
  const currentPassword = values.newPassword || '';

  return (
    <Box sx={passwordValidationStyles}>
      <Typography variant="body2" sx={typographyStyles}>
        Your Password must contain:
      </Typography>
      <List>
        {passwordRequirements.map(requirement => (
          <ListItem key={requirement.id} sx={listItemStyles}>
            <ListItemIcon sx={listItemContainerStyles}>
              {requirement.validator(currentPassword) ? (
                <ValidationRightBgIcon sx={iconStyles} />
              ) : (
                <ValidationWrongBgIcon sx={iconStyles} />
              )}
            </ListItemIcon>
            <Typography
              sx={requirement.validator(currentPassword) ? listItemCheckedTypographyStyles : listItemTypographyStyles}
            >
              {requirement.text}
            </Typography>
          </ListItem>
        ))}
      </List>
    </Box>
  );
};

export interface ResetPasswordPageProps {
  mode: Mode;
}

/**
 * ResetPasswordPage component handles the password reset flow for users.
 *
 * - Extracts the reset token from the URL query parameters.
 * - Renders a form for users to enter and confirm a new password.
 * - Validates the new password against defined requirements.
 * - Submits the new password and token to the backend via a mutation.
 * - Displays error messages for invalid input or failed requests.
 * - Shows a success message and navigation option upon successful password reset.
 *
 * @component
 * @returns {JSX.Element} The rendered Reset Password page.
 */
const ResetPasswordPage: React.FC<ResetPasswordPageProps> = ({mode = Mode.ResetPassword}) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [resetError, setResetError] = useState<string | null>(null);
  const [passwordChanged, setPasswordChanged] = useState(false);
  const token = searchParams.get('code');

  const {
    titleString,
    subTitleString,
    successMessagePart,
    submitButtonTitle,
    isLoading: mutationLoading,
    tokenNotFoundErrorMessage,
    performPasswordAction,
  } = useSetPasswordStateAndAction(mode);

  const canShowGoBackToSignin = () => mode === Mode.ResetPassword;

  const getErrorMessage = (error: unknown) => {
    const errorData =
      typeof error === 'object' && error !== null && 'data' in error
        ? (error as {data?: {message?: string}}).data
        : undefined;

    return errorData?.message;
  };

  const handleSubmit = async (values: {newPassword: string; confirmPassword: string}, actions: AnyObject) => {
    setResetError(null);

    // Check if all password requirements are met
    const allRequirementsMet = passwordRequirements.every(requirement => requirement.validator(values.newPassword));

    if (!allRequirementsMet) {
      setResetError('Please ensure all password requirements are met');
      return;
    }

    try {
      if (!token) {
        setResetError(tokenNotFoundErrorMessage[mode]);
        return;
      }
      await performPasswordAction({newPassword: values.newPassword, token}).unwrap();
      setPasswordChanged(true);
    } catch (error) {
      if (isFetchBaseQueryError(error)) {
        if (error.status === Integers.FourHundredOne) {
          setResetError('The password reset link has expired or is invalid. Please request a new link.');
          return;
        }
      }
      const errorMessage = getErrorMessage(error);
      const message = errorMessage ?? 'Failed to set new password. Please try again.';
      setResetError(message);
    } finally {
      if (actions.setSubmitting) {
        actions.setSubmitting(false);
      }
    }
  };

  const changedPwdSent = () => (
    <StyledButton type="button" variant="contained" fullWidth onClick={() => navigate('/login')}>
      Go To Sign in
    </StyledButton>
  );

  return (
    <>
      {' '}
      {!passwordChanged ? (
        <PageLayout title={titleString[mode]} subtitle={subTitleString[mode]}>
          <Form
            initialValues={initialValues}
            onSubmit={handleSubmit}
            validationSchema={setPasswordValidationSchema}
            validateOnChange={true}
            validateOnBlur={true}
          >
            <FormFieldContainer>
              <FieldLabel variant="body2">New Password</FieldLabel>
              <StyledFormPasswordInput
                name="newPassword"
                id="newPassword"
                placeholder="Set your new password"
                fullWidth
                sx={inputStyles}
                startAdornment={
                  <InputAdornment position="start" sx={{margin: 0}}>
                    <StyledLockIcon />
                    <IconDivider />
                  </InputAdornment>
                }
              />
            </FormFieldContainer>

            <PasswordValidationList />

            <FormFieldContainer>
              <FieldLabel variant="body2">Confirm New Password</FieldLabel>
              <StyledFormPasswordInput
                name="confirmPassword"
                id="confirmPassword"
                placeholder="Confirm your new password"
                fullWidth
                sx={inputStyles}
                startAdornment={
                  <InputAdornment position="start" sx={{margin: 0}}>
                    <StyledLockIcon />
                    <IconDivider />
                  </InputAdornment>
                }
              />
            </FormFieldContainer>

            {resetError && (
              <Box sx={errorStyles(resetError)}>
                <InputAdornment position="start">
                  <ReportProblemOutlinedIcon sx={errorIconStyles} />
                </InputAdornment>
                {resetError}
              </Box>
            )}

            <Box sx={{mt: 3}}>
              <StyledButton type="submit" variant="contained" fullWidth isLoading={mutationLoading}>
                {mutationLoading ? 'Setting Password...' : submitButtonTitle[mode]}
              </StyledButton>
            </Box>
          </Form>

          {canShowGoBackToSignin() && (
            <Box sx={signInWrapper}>
              <Typography sx={signedInTextStyles}>
                Go back to{' '}
                <Box component={'span'} sx={signedInTextStyles}>
                  <a href="/login">Sign in</a>
                </Box>
              </Typography>
            </Box>
          )}
        </PageLayout>
      ) : (
        <PageLayout
          isPasswordSent={true}
          title={`You’re all set! Password ${successMessagePart[mode]} successfully!`}
          subtitle="You can now log in with your new password."
        >
          {changedPwdSent()}
        </PageLayout>
      )}
    </>
  );
};

export default ResetPasswordPage;
