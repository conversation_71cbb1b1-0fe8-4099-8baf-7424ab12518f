import {Box, BoxProps, Grid, Icon<PERSON>utton, <PERSON>, Typography} from '@mui/material';
import DeleteIcon from 'Assets/DeleteIcon';
import FormInput from 'Components/Forms/FormInput';
import FormSelect from 'Components/Forms/FormSelect/FormSelect';
import {FieldArray, getIn, useFormikContext} from 'formik';
import {Integers} from 'Helpers/integers';
import {StyleUtils} from 'Helpers/styleUtils';
import {FC} from 'react';
import {useGetRolesQuery} from 'redux/app/tenantManagementApiSlice';
import {AddUserPageMode} from '../addUser.util';
import {UserForm} from './AddUserPageBottomActions';

/**
 * Formik values for the Add User page
 */
export interface AddUserPageFormikValues {
  items: UserForm[];
}

/**
 * Props for the Formik field array
 * Represents the props for the Formik field array in the Add User page
 */
interface FormikFieldArrayProps extends BoxProps {
  mode: AddUserPageMode;
}

/**
 * Formik field array for the Add User page
 * @param param - Props for the Formik field array
 * @returns JSX.Element
 */
const AddUserPageFormikForm: FC<FormikFieldArrayProps> = ({mode, ...rest}) => {
  const {values, errors, touched} = useFormikContext<{items: UserForm[]}>();
  const {
    data: roles,
    isLoading: isLoadingRoles,
    error: rolesError,
  } = useGetRolesQuery({
    order: 'name asc',
  });

  const renderNoRole = () => {
    return (
      <Box sx={{p: 1.5}}>
        <Typography sx={{mb: 0, fontWeight: 600, fontSize: '0.875rem', color: 'body.800'}}>
          No roles created to select
        </Typography>
        <Link
          component="button"
          color="secondary.main"
          variant="body2"
          underline="hover"
          fontSize="0.75rem"
          fontWeight={700}
        >
          + Add role
        </Link>
      </Box>
    );
  };

  return (
    <Box {...rest}>
      <FieldArray name="items">
        {({push, remove}) => {
          const arraySize = values.items.length;
          const size: number = Integers.Four;
          const showDeleteButton = arraySize > 1;
          const marginRight = showDeleteButton ? '3.5rem' : 0;
          return (
            <>
              {values.items.map((_, index) => (
                <Grid container spacing={2} sx={{mb: 2}} key={'id' + index}>
                  <Grid size={{xs: 12, md: size}}>
                    <Typography sx={StyleUtils.lalelStyles}>Full Name*</Typography>
                    <FormInput
                      id={`items.${index}.fullName`}
                      name={`items.${index}.fullName`}
                      fullWidth
                      required
                      sx={{...StyleUtils.inputBoxStyles, mr: marginRight}}
                      readOnly={false}
                      errorMessage={
                        getIn(touched, `items.${index}.fullName`) && getIn(errors, `items.${index}.fullName`)
                          ? getIn(errors, `items.${index}.fullName`)
                          : ''
                      }
                      placeholder="Enter full name"
                    />
                  </Grid>
                  <Grid size={{xs: 12, md: size}}>
                    <Typography sx={StyleUtils.lalelStyles}>Email*</Typography>
                    <FormInput
                      fullWidth
                      id={`items.${index}.email`}
                      name={`items.${index}.email`}
                      required
                      sx={{...StyleUtils.inputBoxStyles, mr: marginRight}}
                      readOnly={mode == 'edit'}
                      disabled={mode == 'edit'}
                      errorMessage={
                        getIn(touched, `items.${index}.email`) && getIn(errors, `items.${index}.email`)
                          ? getIn(errors, `items.${index}.email`)
                          : ''
                      }
                      placeholder="Enter email"
                    />
                  </Grid>
                  <Grid size={{xs: 12, md: size}}>
                    <Typography sx={StyleUtils.lalelStyles}>Role*</Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'flex-start',
                        gap: 1,
                      }}
                    >
                      <Box
                        component={'span'}
                        sx={{
                          flex: 1,
                          overflow: 'hidden',
                        }}
                      >
                        <FormSelect
                          options={roles?.map(role => ({label: role.name, value: role.id})) ?? []}
                          data-testid="role-select"
                          id={`items.${index}.role`}
                          name={`items.${index}.role`}
                          disabled={isLoadingRoles || !!rolesError}
                          placeholder="Select role"
                          sx={{
                            ...StyleUtils.selectBox,
                            flex: 1,
                            maxHeight: '2.625rem',
                            borderRadius: StyleUtils.inputBoxStyles.borderRadius,
                            height: '2.625rem',
                            '& .Mui-disabled': {
                              '-webkit-text-fill-color': '',
                            },
                          }}
                          errorMessage={
                            getIn(touched, `items.${index}.role`) && getIn(errors, `items.${index}.role`)
                              ? getIn(errors, `items.${index}.role`)
                              : ''
                          }
                          noItemRenderer={renderNoRole}
                        />
                      </Box>
                      {showDeleteButton && (
                        <IconButton sx={{height: '2.625rem', aspectRatio: 1}} onClick={() => remove(index)}>
                          <DeleteIcon />
                        </IconButton>
                      )}
                    </Box>
                  </Grid>
                </Grid>
              ))}
              {errors.items && typeof errors.items === 'string' && (
                <Box sx={{mt: 2, display: 'flex', justifyContent: 'end', mr: marginRight, mb: 2}}>
                  <Typography color="error">{errors.items}</Typography>
                </Box>
              )}
              {mode !== 'edit' && (
                <Box sx={{mb: 3, display: 'flex', justifyContent: 'flex-end', mr: marginRight}}>
                  <Link
                    component="button"
                    underline="hover"
                    variant="body2"
                    sx={{
                      color: 'secondary.main',
                      fontSize: 12,
                      fontWeight: 700,
                    }}
                    onClick={() => {
                      push({fullName: '', email: '', role: ''});
                    }}
                  >
                    + Add another user
                  </Link>
                </Box>
              )}
            </>
          );
        }}
      </FieldArray>
    </Box>
  );
};
export default AddUserPageFormikForm;
