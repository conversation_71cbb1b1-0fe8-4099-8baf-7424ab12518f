import {BreadcrumbItem} from 'Components/Breadcrumb/Breadcrumb';
import {emailRegEx} from 'Constants/enums';
import {Integers} from 'Helpers/integers';
import {useLocation} from 'react-router';
import {UserViewType} from 'redux/app/types';
import {RouteNames} from 'Routes/routeNames';
import * as Yup from 'yup';

export type AddUserPageMode = 'add' | 'edit';
/**
 * Metadata for the Add User page location
 */
interface AddUserLocationMetadata {
  mode: AddUserPageMode;
  user: UserViewType | undefined;
}

const maxEmailLength = 254;

function getDuplicateEmails(list: {email: string}[]): {email: string; index: number}[] {
  const seen = new Map<string, number>(); // email → first index
  const duplicates: {email: string; index: number}[] = [];
  const addedFirst = new Set<string>(); // track if first index was already added

  list.forEach((item, index) => {
    const existingIndex = seen.get(item.email);

    if (existingIndex !== undefined) {
      // Add the first occurrence once
      if (!addedFirst.has(item.email)) {
        duplicates.push({email: item.email, index: existingIndex});
        addedFirst.add(item.email);
      }
      // Add this duplicate occurrence
      duplicates.push({email: item.email, index});
    } else {
      seen.set(item.email, index);
    }
  });

  return duplicates;
}

/**
 * Get the validation schema for the Add User form
 * @param roles - Array of user roles
 * @param checkEmail - Function to check if email is unique
 * @returns Yup validation schema
 */
export const getAddUserFormValidationSchema = (
  roles: string[],
  checkEmail?: (email: string, path: string) => Promise<boolean>,
) =>
  Yup.object({
    items: Yup.array()
      .of(
        Yup.object().shape({
          fullName: Yup.string()
            .trim()
            .required('Full name is required')
            .min(Integers.Seven, 'Full name should have at least 7 characters')
            .max(Integers.OneHundred, 'Full name should have at most 100 characters')
            .matches(/^[A-Za-z\s]+$/, 'Full name should only contain letters'),
          email: Yup.string()
            .trim()
            .required('Email address is required')
            .min(Integers.Five, 'Email address should have at least 5 characters')
            .max(maxEmailLength, 'Email address should have at most 254 characters')
            .matches(emailRegEx, 'Email address is not in a valid format')
            .test('is-unique-email', 'Email address already exists', async (value, meta) => {
              if (!checkEmail) {
                return true; // skip check if no function provided
              }
              return await checkEmail(value, meta.path);
            }),
          role: Yup.string().required('Role is required').oneOf(roles, 'Invalid role'),
        }),
      )
      .test('unique-email', 'Duplicate emails are not allowed', async (list, meta) => {
        if (!list) {
          return true; // skip check if list is empty or invalid
        }
        if (!checkEmail) {
          return true; // skip check if no function provided
        }

        const duplicateEmails = getDuplicateEmails(list);
        if (duplicateEmails.length === 0) {
          return true; // all emails are unique
        }
        let errs = duplicateEmails.map(({index}) => {
          return meta.createError({
            path: `${meta.path}.${index}.email`,
            message: 'Duplicate email detected. Please use a unique email.',
          });
        });
        return new Yup.ValidationError(errs);
      }),
  });

/**
 * Get the breadcrumb items for the Add User page
 * @param mode - The mode of the page (add/edit)
 * @returns Array of breadcrumb items
 */
export const breadcrumbsData = (mode: AddUserPageMode): BreadcrumbItem[] => {
  const isEditMode = mode === 'edit';
  return [
    {label: 'User Management', url: RouteNames.USER_MANAGEMENT},
    {
      label: isEditMode ? 'Edit User' : 'Add User',
      url: isEditMode ? RouteNames.EDIT_USER : RouteNames.ADD_USER,
    },
  ];
};

/**
 * Get the location metadata for the Add User page
 * @returns Location metadata
 */
export const useGetAddUserLocationMetaData = (): AddUserLocationMetadata => {
  const location = useLocation();
  return {
    mode: location.pathname.includes(RouteNames.EDIT_USER) ? 'edit' : 'add',
    user: location.state?.user,
  };
};
