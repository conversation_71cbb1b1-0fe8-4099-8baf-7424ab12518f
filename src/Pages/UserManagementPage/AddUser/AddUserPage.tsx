import {Box, Divider} from '@mui/material';
import BackdropLoader from 'Components/BackdropLoader';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import Form from 'Components/Forms/Form';
import {FormActions} from 'Components/Forms/Form/FormUtils';
import {isAnyActive} from 'Helpers/utils';
import {enqueueSnackbar} from 'notistack';
import {useEffect, useRef} from 'react';
import {useNavigate} from 'react-router';
import {useAddUserMutation, useGetRolesQuery, useUpdateUserMutation} from 'redux/app/tenantManagementApiSlice';
import {CreateTenantUserDto, UpdateTenantUserDto} from 'redux/app/types';
import {RouteNames} from 'Routes/routeNames';
import {breadcrumbsData, getAddUserFormValidationSchema, useGetAddUserLocationMetaData} from './addUser.util';
import AddUserPageBottomAction from './components/AddUserPageBottomActions';
import AddUserPageFormikForm, {AddUserPageFormikValues} from './components/AddUserPageForm';
import {useCheckExistingEmail} from './hooks/useCheckExistingEmail';

/**
 * AddUsersPage component handles the logic and UI for adding or editing users in the User Management section.
 *
 * - Fetches roles and user metadata for form initialization.
 * - Supports both "add" and "edit" modes, determined by location metadata.
 * - Handles form submission for adding new users or updating existing users.
 * - Displays loading and error states during data fetching.
 * - Utilizes Formik for form state management and validation.
 * - Shows success/error notifications using notistack.
 * - Navigates to appropriate routes after successful operations.
 *
 * @component
 * @returns {React.FC} The AddUsersPage component.
 */
const AddUsersPage: React.FC = () => {
  const {mode, user} = useGetAddUserLocationMetaData();
  const {
    data: roles,
    isLoading: isLoadingRoles,
    error: rolesError,
  } = useGetRolesQuery(
    {order: 'name asc'},
    {
      refetchOnMountOrArgChange: true,
    },
  );
  const [updateUser] = useUpdateUserMutation();
  const [addUsers] = useAddUserMutation();
  const {checkEmailAddressExists} = useCheckExistingEmail();
  const navigator = useNavigate();

  const getInitialValue = (): AddUserPageFormikValues => {
    return {
      items: [
        {
          email: user?.email ?? '',
          fullName: user?.firstName ?? '',
          role: user?.roleId ?? '',
        },
      ],
    };
  };

  const initialValues = useRef(getInitialValue());

  useEffect(() => {
    if (mode == 'edit' && !user) {
      navigator(RouteNames.HOME, {
        replace: true,
      });
    }
  }, [user, mode]);

  /**
   * Updates user information.
   *
   * @param {UpdateTenantUserDto} userDto - The user data to update.
   * @returns {Promise<void>}
   */
  const updateUserInfoHandler = async (userDto: UpdateTenantUserDto) => {
    try {
      await updateUser(userDto).unwrap();
      enqueueSnackbar('User updated successfully!', {
        variant: 'success',
        subMessage: userDto.fullName,
      });
      navigator(RouteNames.USER_MANAGEMENT, {replace: true});
    } catch {
      enqueueSnackbar('Failed to update user', {
        variant: 'error',
      });
    }
  };
  /**
   * Adds new users.
   *
   * @param items - The user data to add.
   * @param action - The form actions.
   */
  const addUsersInfoHandler = async (items: CreateTenantUserDto[], action: FormActions<AddUserPageFormikValues>) => {
    try {
      await addUsers(items).unwrap();
      enqueueSnackbar('Users created successfully!', {
        variant: 'success',
        subMessage: `${items.map(itm => itm.fullName).join(', ')}`,
      });
      action.resetForm({
        values: getInitialValue(),
      });
      navigator(RouteNames.USER_MANAGEMENT, {replace: true});
    } catch {
      enqueueSnackbar('Failed to add user(s)', {
        variant: 'error',
      });
    }
  };

  /**
   * Handles form submission.
   *
   * @param formValues - The form values.
   * @param action - The form actions.
   */
  const onSubmit = async (formValues: AddUserPageFormikValues, action: FormActions<AddUserPageFormikValues>) => {
    if (mode === 'edit') {
      const userData = formValues.items[0];
      await updateUserInfoHandler({
        userId: user?.id || '',
        fullName: userData.fullName.trim(),
        roleId: userData.role.trim(),
      });
    } else {
      await addUsersInfoHandler(
        formValues.items.map(item => ({
          email: item.email.trim(),
          fullName: item.fullName.trim(),
          roleId: item.role,
        })),
        action,
      );
    }
  };

  if (isAnyActive([isLoadingRoles])) {
    return <BackdropLoader />;
  }

  if (isAnyActive([Boolean(rolesError)])) {
    return <div>Something went wrong</div>;
  }

  return (
    <Box>
      <Box sx={{mb: 1}}>
        <Breadcrumb items={breadcrumbsData(mode)} separator="|" showHeader />
      </Box>
      <Box
        sx={{
          py: 1.5,
          border: '1px solid',
          borderColor: 'body.200',
          gap: 1.5,
          borderRadius: '0.375rem',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Form
          initialValues={initialValues.current}
          validateOnChange={true}
          validationSchema={getAddUserFormValidationSchema(
            (roles ?? [])?.map(role => role.id),
            mode == 'add' ? checkEmailAddressExists : undefined,
          )}
          onSubmit={(v, a) => onSubmit(v, a)}
        >
          <Box sx={{display: 'flex', flexDirection: 'column', flex: 1, minHeight: '80vh'}}>
            <AddUserPageFormikForm mode={mode} sx={{px: 1.5}} />
            <Divider sx={{my: 2, mt: 'auto'}} />
            <AddUserPageBottomAction mode={mode} sx={{px: 1.5}} />
          </Box>
        </Form>
      </Box>
    </Box>
  );
};

export default AddUsersPage;
