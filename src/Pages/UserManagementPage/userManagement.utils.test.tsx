import type {CellContext} from '@tanstack/react-table';
import {describe, expect, it} from 'vitest';
import {
  DEFAULT_LIMIT,
  DEFAULT_OFFSET,
  getBackendColumnName,
  getUserStatusLabel,
  userColumns,
  UserStatus,
} from './userManagement.utils';

describe('userManagement.utils', () => {
  it('UserStatus enum values', () => {
    expect(UserStatus.PENDINGPROVISION).toBe(0);
    expect(UserStatus.ACTIVE).toBe(1);
    expect(UserStatus.INACTIVE).toBe(2);
  });

  it('getUserStatusLabel returns correct label', () => {
    expect(getUserStatusLabel(UserStatus.ACTIVE)).toBe('Active');
    expect(getUserStatusLabel(UserStatus.INACTIVE)).toBe('Inactive');
    expect(getUserStatusLabel(UserStatus.PENDINGPROVISION)).toBe('Pending activation');
    expect(getUserStatusLabel(999)).toBe('Pending activation');
  });

  it('getBackendColumnName maps known columns and falls back for unknown', () => {
    expect(getBackendColumnName('name')).toBe('firstName');
    expect(getBackendColumnName('email')).toBe('email');
    expect(getBackendColumnName('role')).toBe('roleName');
    expect(getBackendColumnName('status')).toBe('status');
    expect(getBackendColumnName('createdDate')).toBe('createdOn');
    expect(getBackendColumnName('modifiedDate')).toBe('modifiedOn');
    expect(getBackendColumnName('unknown')).toBe('unknown');
  });

  it('DEFAULT_LIMIT and DEFAULT_OFFSET are correct', () => {
    expect(DEFAULT_LIMIT).toBe(5);
    expect(DEFAULT_OFFSET).toBe(0);
  });

  it('userColumns configures all expected columns', () => {
    const headers = userColumns.map(col => col.header);
    expect(headers).toContain('Name');
    expect(headers).toContain('Email address');
    expect(headers).toContain('Role');
    expect(headers).toContain('Status');
    expect(headers).toContain('Created on');
    expect(headers).toContain('Modified on');
    expect(headers).toContain('Actions');
  });

  it('userColumns cell renderers do not throw', () => {
    // Minimal mock row for cell renderers
    const row = {
      original: {
        id: '1',
        firstName: 'Test',
        email: '<EMAIL>',
        roleName: 'Admin',
        status: UserStatus.ACTIVE,
        createdOn: new Date().toISOString(),
        modifiedOn: new Date().toISOString(),
      },
    };

    // Minimal mock columnDef and context for Action column
    const mockMeta = {onAction: vi.fn()};
    const mockColumnDef = {meta: mockMeta};
    const mockColumn = {columnDef: mockColumnDef};

    userColumns.forEach(col => {
      if (col.cell && typeof col.cell === 'function') {
        // For Actions column, provide full context
        const ctx = {
          row,
          column: mockColumn,
          columnDef: mockColumnDef,
          getValue: () => undefined,
          renderValue: () => undefined,
          table: {},
          getContext: () => ({}),
        } as unknown as CellContext<typeof row.original, typeof mockMeta>;
        expect(() => col.cell?.(ctx)).not.toThrow();
      }
    });
  });

  it('calls onAction with correct arguments if onAction exists', () => {
    const onAction = vi.fn();
    const ctx = {
      column: {
        columnDef: {
          meta: {onAction},
        },
      },
      row: {original: {id: 1, name: 'Test'}},
    };
    const handler = (action: string) =>
      (ctx.column.columnDef.meta as any)?.onAction
        ? (ctx.column.columnDef.meta as any).onAction(action, ctx.row.original)
        : undefined;

    handler('test-action');
    expect(onAction).toHaveBeenCalledWith('test-action', {id: 1, name: 'Test'});
  });

  it('returns undefined if onAction does not exist', () => {
    const ctx = {
      column: {
        columnDef: {
          meta: {},
        },
      },
      row: {original: {id: 2, name: 'NoAction'}},
    };
    const handler = (action: string) =>
      (ctx.column.columnDef.meta as any)?.onAction
        ? (ctx.column.columnDef.meta as any).onAction(action, ctx.row.original)
        : undefined;

    expect(handler('test-action')).toBeUndefined();
  });
});
