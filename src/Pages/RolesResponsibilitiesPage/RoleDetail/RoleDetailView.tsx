import {MoreHoriz} from '@mui/icons-material';

import {Box, Divider, Grid, ListItemText, Menu, MenuItem, Typography} from '@mui/material';
import EditIcon from 'Assets/EditIcon';
import BackdropLoader from 'Components/BackdropLoader';
import BorderButton from 'Components/BorderButton/BorderButton';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import EllipsisText from 'Components/EllipsisText/EllipsisText';
import {usePermissions} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import PermissionsUI from 'Components/PermissionView/PermissionView';
import PermissionWrapper from 'Components/PermissionWrapper';
import RoleStatusChip from 'Components/RoleStatusChip/RoleStatusChip';
import {defaultDateFormat} from 'Constants/enums';
import PermissionKey from 'Constants/enums/permissions';
import {Integers} from 'Helpers/integers';
import {getFullName} from 'Helpers/utils';
import {useCallback, useState} from 'react';
import {useNavigate} from 'react-router';
import {useActivateDeactiveRoleMutation} from 'redux/auth/authApiSlice';
import {RouteNames} from 'Routes/routeNames';
import {detailPageSubtitleLabelSx, detailPageTitleLabelSx, gridSize} from 'styles/pages/Common.styles';
import {ActionType, ActivateDeactivateDialog} from '../ActivateDeactiveDialog/ActivateDeactivateDialog';
import {RoleStatus} from '../roles.util';
import useRoleDetailState from './hook/useRoleDetailState';
import {useGetRoleDetailLocationMetaData} from './roleDetail.util';

const PX = 1.5;

export default function RoleDetailView() {
  const {roleView: passedRoleView} = useGetRoleDetailLocationMetaData();
  const {hasPermission} = usePermissions();
  const {role, loadingRole, roleError, createdByUser} = useRoleDetailState(
    passedRoleView?.roleId,
    !hasPermission(PermissionKey.ViewTenantUser),
  );

  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const [activateDeactivateRole, {isLoading: performingActivatingDeactivating}] = useActivateDeactiveRoleMutation();
  const [showActivateDeactivate, setShowActivateDeactivate] = useState<boolean>(false);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDeactivateUser = () => {
    setShowActivateDeactivate(true);
    handleMenuClose();
  };

  const activateDeactivateRoleAction = useCallback(async () => {
    if (!role) return;
    try {
      if (!showActivateDeactivate) return;
      const markActive = role.status === RoleStatus.INACTIVE;
      await activateDeactivateRole({id: role.id, isActive: markActive}).unwrap();
      setShowActivateDeactivate(false);
      navigate(RouteNames.ROLES_RESPONSIBILITIES, {replace: true});
    } catch {
      // Error handled at slice level
    }
  }, [activateDeactivateRole, showActivateDeactivate]);

  const breadcrumbs = () => {
    return [
      {label: 'Role & Permissions', url: RouteNames.ROLES_RESPONSIBILITIES},
      {
        label: role?.name ?? '-',
        url: RouteNames.VIEW_ROLE,
      },
    ];
  };

  const buildCreatedBy = () => {
    if (!hasPermission(PermissionKey.ViewTenantUser)) return;
    const renderingCondition = createdByUser && !roleError && !loadingRole;
    return (
      renderingCondition && (
        <Grid size={gridSize}>
          <Typography sx={detailPageTitleLabelSx}>Created by</Typography>
          <EllipsisText
            text={getFullName(createdByUser) ?? '-'}
            sx={{...detailPageSubtitleLabelSx}}
            data-testid="created-by"
          />
        </Grid>
      )
    );
  };

  const buildBottomSection = () => {
    return (
      <Box sx={{display: 'flex', flexDirection: 'column', mt: 'auto', mb: 1.5}}>
        <Divider sx={{my: 2}} />
        <Box sx={{alignSelf: 'flex-end'}} px={PX}>
          <BorderButton
            sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
            fullWidth={false}
            onClick={() => navigate(-1)}
          >
            Close
          </BorderButton>
        </Box>
      </Box>
    );
  };

  const buildPermissionSection = () => {
    const hasPermissionArr = role?.permissions && role.permissions.length > 0;
    return (
      hasPermissionArr && (
        <Box sx={{mx: PX}}>
          <Divider sx={{my: 3}} />
          <PermissionsUI permissions={role?.permissions ?? []} />
        </Box>
      )
    );
  };

  const buildTopPart = () => {
    if (!role) return null;
    return (
      <Box display="flex" alignItems="center" gap={1}>
        <Box display={'flex'} flexDirection={'column'} gap={1}>
          <Box display={'flex'} flexDirection={'row'} alignItems="center" gap={1}>
            <EllipsisText text={role.name ?? '-'} sx={{fontSize: '1.25rem', fontWeight: 700, colors: 'body.dark'}} />
            <RoleStatusChip role={passedRoleView} />
          </Box>
          <Breadcrumb items={breadcrumbs()} separator="|" />
        </Box>
        <Box sx={{ml: 'auto', display: 'flex', flexDirection: 'row', gap: 1}}>
          {hasPermission(PermissionKey.UpdateRole) && role.status === RoleStatus.ACTIVE && (
            <BorderButton
              sx={{fontWeight: 700, px: '1.56rem', height: '2.5rem'}}
              onClick={() => {
                navigate(RouteNames.EDIT_ROLE, {
                  state: {
                    role: passedRoleView,
                  },
                });
              }}
            >
              <Box sx={{display: 'flex', alignItems: 'center', gap: 0.5}}>
                <EditIcon sx={{fill: 'white', width: '1.2rem'}} />
                Edit
              </Box>
            </BorderButton>
          )}

          {/* add menu button having deactivate user button in it  */}
          <PermissionWrapper permission={PermissionKey.UpdateRole}>
            <BorderButton sx={{px: 0, minWidth: 0, width: '2.5rem', height: '2.5rem'}} onClick={handleMenuOpen}>
              <MoreHoriz sx={{fill: theme => theme.palette.body[Integers.FiveHundred]}} />
            </BorderButton>
          </PermissionWrapper>
        </Box>
      </Box>
    );
  };

  if (loadingRole) {
    return <BackdropLoader />;
  }

  if (!passedRoleView) {
    return 'Something went wrong';
  }

  if (!role) {
    return '';
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 0.5,
      }}
    >
      {buildTopPart()}
      {/* Header */}

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1.5,
        }}
      >
        <Box
          sx={{
            border: '1px solid',
            borderColor: 'body.200',
            borderRadius: '0.375rem',
            pt: 1,
            minHeight: '80vh',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Typography sx={{fontSize: '1rem', fontWeight: 700, color: 'body:900'}} px={PX}>
            Role information
          </Typography>

          <Grid container spacing={2} px={PX} mt={1}>
            <Grid size={gridSize}>
              <Typography sx={detailPageTitleLabelSx}>No. of users</Typography>
              <Typography sx={detailPageSubtitleLabelSx}>{passedRoleView.userCount}</Typography>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={detailPageTitleLabelSx}>Created on</Typography>
              <Typography sx={detailPageSubtitleLabelSx}>
                {defaultDateFormat(passedRoleView?.createdOn ?? '')}
              </Typography>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={detailPageTitleLabelSx}>Modified on</Typography>
              <Typography sx={detailPageSubtitleLabelSx}>
                {defaultDateFormat(passedRoleView?.modifiedOn ?? '')}
              </Typography>
            </Grid>

            {buildCreatedBy()}
          </Grid>
          {buildPermissionSection()}

          {buildBottomSection()}
        </Box>
        <Menu
          id="user-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <MenuItem onClick={handleDeactivateUser}>
            <ListItemText>{role.status === RoleStatus.ACTIVE ? 'Deactivate' : 'Activate'} </ListItemText>
          </MenuItem>
        </Menu>
      </Box>
      <ActivateDeactivateDialog
        actionType={(() => {
          switch (role.status) {
            case RoleStatus.ACTIVE:
              if (passedRoleView.userCount > Integers.Zero) {
                return ActionType.DeactivateNotAllowed;
              }
              return ActionType.Deactivate;
            case RoleStatus.INACTIVE:
              return ActionType.Activate;
          }
          return ActionType.Activate;
        })()}
        onClose={() => {
          if (performingActivatingDeactivating) return;
          setShowActivateDeactivate(false);
        }}
        onConfirm={activateDeactivateRoleAction}
        isLoading={performingActivatingDeactivating}
        open={showActivateDeactivate}
        title={role.name ?? ''}
      />
    </Box>
  );
}
