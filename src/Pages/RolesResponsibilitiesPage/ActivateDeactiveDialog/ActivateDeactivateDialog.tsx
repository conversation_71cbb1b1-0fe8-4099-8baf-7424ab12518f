import {Typography} from '@mui/material';
import ActivateConfirmationIcon from 'Assets/confirmation-icons/ActivateConfirmIcon';
import DeactivateConfirmIcon from 'Assets/confirmation-icons/DeactivateConfirmIcon';
import {BaseDialogLayout} from 'Components/BaseDialogLayout';

export enum ActionType {
  Activate = 'Activate',
  Deactivate = 'Deactivate',
  DeactivateNotAllowed = 'NotAllowed',
}

interface ActivateDeactivateDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  actionType: ActionType;
  title: string;
  isLoading: boolean;
}

export const ActivateDeactivateDialog = ({
  open,
  onClose,
  onConfirm,
  actionType,
  title,
  isLoading,
}: ActivateDeactivateDialogProps) => {
  const icon =
    actionType === ActionType.Activate ? (
      <ActivateConfirmationIcon sx={{fill: 'transparent'}} />
    ) : (
      <DeactivateConfirmIcon sx={{fill: 'transparent'}} />
    );

  const titleMsg = actionType === ActionType.Activate ? 'Activate Role' : 'Deactivate Role';

  const message = (() => {
    switch (actionType) {
      case ActionType.Activate:
        return 'Are you sure you want to activate this role?';
      case ActionType.Deactivate:
        return 'Are you sure you want to deactivate this role?';
      case ActionType.DeactivateNotAllowed:
        return 'Role deactivation is not allowed while it is assigned to active users.';
    }
  })();

  const caption = (() => {
    if (actionType === ActionType.DeactivateNotAllowed) {
      return (
        <Typography sx={{fontSize: '0.875rem', fontWeight: 400, color: 'body.500', textAlign: 'center'}}>
          Please reassign or remove users from this role before attempting to deactivate it.
        </Typography>
      );
    }
  })();

  const confirmLabel = actionType === ActionType.Activate ? 'Activate' : 'Deactivate';
  const cancelLabel = actionType === ActionType.DeactivateNotAllowed ? 'Close' : 'Cancel';
  const iconBGColor = actionType === ActionType.Activate ? 'alert.success.bg' : 'alert.error.bg';

  return (
    <>
      {open && (
        <BaseDialogLayout
          open={open}
          onClose={onClose}
          onConfirm={onConfirm}
          confirmLabel={actionType !== ActionType.DeactivateNotAllowed ? confirmLabel : undefined}
          title={titleMsg}
          message={message}
          highlightText={title}
          icon={icon}
          isLoading={isLoading}
          caption={caption}
          maxWidth={400}
          iconBGColor={iconBGColor}
          cancelLabel={cancelLabel}
        />
      )}
    </>
  );
};
