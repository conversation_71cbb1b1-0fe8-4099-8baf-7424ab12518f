/* @vitest-environment jsdom */
import {screen, waitFor} from '@testing-library/react';
import {dashboardTenantStatuses} from 'redux/app/types/tenant.type';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {describe, expect, it, vi} from 'vitest';
import Home from './Home';
import {TenantStatus} from './Tenants/tenants.utils';

// Mock dependencies
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useGetTenantStatusMetricsQuery: () => ({
    data: {
      status: {
        '1': {count: 5, status: 'Active'},
        '2': {count: 2, status: 'Inactive'},
      },
      tenants: [
        {id: 1, name: 'Tenant 1'},
        {id: 2, name: 'Tenant 2'},
      ],
    },
    error: undefined,
  }),
}));
vi.mock('redux/auth/authApiSlice', () => ({
  useGetUserQuery: () => ({
    data: {firstName: 'john'},
    isLoading: false,
  }),
}));
vi.mock('Components/PermissionRedirectWrapper/PermissionProvider', () => ({
  usePermissions: () => ({
    hasPermission: () => true,
  }),
}));
vi.mock('react-router', () => ({
  useNavigate: () => vi.fn(),
}));
vi.mock('./Dashboard/DashboardTable', () => ({
  __esModule: true,
  default: () => <div data-testid="DashboardTable" />,
}));
vi.mock('./Dashboard/TenantOverview', () => ({
  __esModule: true,
  default: () => <div data-testid="TenantOverview" />,
}));
vi.mock('./Dashboard/CustomBarChart/CustomBarChart', () => ({
  default: () => <div data-testid="CustomBarChart" />,
}));

describe('Home', () => {
  it('should render the HomePage container', () => {
    renderWithTheme(<Home />);
    expect(screen.getByTestId('HomePage')).toBeInTheDocument();
  });

  it('should render the greeting with user name', async () => {
    renderWithTheme(<Home />);
    await waitFor(() => {
      expect(screen.getByText(/Hi John!👋/i)).toBeInTheDocument();
    });
  });

  it('should render TenantOverview and DashboardTable', () => {
    renderWithTheme(<Home />);
    expect(screen.getByTestId('TenantOverview')).toBeInTheDocument();
    expect(screen.getByTestId('DashboardTable')).toBeInTheDocument();
  });

  it('should render the BarChart', () => {
    renderWithTheme(<Home />);
    expect(screen.getAllByTestId('CustomBarChart').length).greaterThan(0);
  });

  it('should filter statusMetrics to include at least all dashboardTenantStatuses', () => {
    // Create a statusMetrics object with all possible TenantStatus keys (as string)
    const allStatuses = Object.values(TenantStatus)
      .filter(v => typeof v === 'number')
      .map(v => v as number);

    const statusMetrics = Object.fromEntries(
      allStatuses.map(key => [key.toString(), {count: 1, status: TenantStatus[key as TenantStatus]}]),
    );

    // Simulate the filter logic from Home.tsx
    const filteredKeys = Object.keys(statusMetrics).filter(key => dashboardTenantStatuses.has(Number(key)));

    // Assert that all dashboardTenantStatuses are present in the filtered result
    dashboardTenantStatuses.forEach((status: TenantStatus) => {
      expect(filteredKeys).toContain(status.toString());
    });
  });
});
